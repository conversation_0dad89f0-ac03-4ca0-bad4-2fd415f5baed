import { Link, useNavigate } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'
import styles from './Header.module.css'

const Header = () => {
  const { isAuthenticated, user, logout } = useAuthStore()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <Link to="/" className={styles.logo}>
          ☢️ NuclearStory
        </Link>

        <nav className={styles.nav}>
          {isAuthenticated ? (
            <>
              <Link
                to="/menu"
                className={`${styles.navButton} ${styles.playButton}`}
              >
                Play Game
              </Link>
              <span className={styles.welcomeText}>
                Welcome, {user?.username}
              </span>
              <button
                onClick={handleLogout}
                className={`${styles.navButton} ${styles.logoutButton}`}
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to="/login"
                className={`${styles.navButton} ${styles.loginButton}`}
              >
                Login
              </Link>
              <Link
                to="/signup"
                className={`${styles.navButton} ${styles.signupButton}`}
              >
                Sign Up
              </Link>
            </>
          )}
        </nav>
      </div>
    </header>
  )
}

export default Header
