// Экспорт всех игровых данных
export * from './weapons';
export * from './armor';
export * from './items';

// Импорты для удобства
import { WEAPONS } from './weapons';
import { ARMOR } from './armor';
import { ALL_ITEMS, CONSUMABLES, TOOLS, RESOURCES } from './items';

// Объединенные коллекции
export const GAME_DATA = {
  weapons: WEAPONS,
  armor: ARMOR,
  items: ALL_ITEMS,
  consumables: CONSUMABLES,
  tools: TOOLS,
  resources: RESOURCES
};

// Утилиты для поиска предметов
export const findWeaponById = (id: string) => WEAPONS.find(weapon => weapon.id === id);
export const findArmorById = (id: string) => ARMOR.find(armor => armor.id === id);
export const findItemById = (id: string) => ALL_ITEMS.find(item => item.id === id);

// Фильтры по редкости
export const getWeaponsByRarity = (rarity: string) => WEAPONS.filter(weapon => weapon.rarity === rarity);
export const getArmorByRarity = (rarity: string) => ARMOR.filter(armor => armor.rarity === rarity);
export const getItemsByRarity = (rarity: string) => ALL_ITEMS.filter(item => item.rarity === rarity);

// Фильтры по типу
export const getWeaponsByType = (weaponType: string) => WEAPONS.filter(weapon => weapon.weaponType === weaponType);
export const getArmorByType = (armorType: string) => ARMOR.filter(armor => armor.armorType === armorType);

// Статистика
export const GAME_STATS = {
  totalWeapons: WEAPONS.length,
  totalArmor: ARMOR.length,
  totalItems: ALL_ITEMS.length,
  totalConsumables: CONSUMABLES.length,
  totalTools: TOOLS.length,
  totalResources: RESOURCES.length
};
