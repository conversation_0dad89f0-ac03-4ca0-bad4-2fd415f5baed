import React, { useState } from 'react'
import { useUserWorlds, useWorldManagement } from '../hooks/useWorlds'
import { WorldSummary } from '../api/worldsApi'

interface WorldsListProps {
  userId: string
  onWorldSelect?: (worldId: string) => void
  onWorldLoad?: (worldId: string) => void
}

export const WorldsList: React.FC<WorldsListProps> = ({ 
  userId, 
  onWorldSelect, 
  onWorldLoad 
}) => {
  const [statusFilter, setStatusFilter] = useState<'active' | 'archived' | 'all'>('active')
  const [selectedWorld, setSelectedWorld] = useState<string | null>(null)
  
  const { worlds, loading, error, pagination, refresh, loadPage } = useUserWorlds(userId, statusFilter)
  const { updateWorld, archiveWorld, deleteWorld, loading: managementLoading } = useWorldManagement()

  const handleWorldAction = async (action: string, world: WorldSummary) => {
    try {
      switch (action) {
        case 'load':
          onWorldLoad?.(world.id)
          break
          
        case 'archive':
          await archiveWorld(world.id, userId)
          refresh()
          break
          
        case 'unarchive':
          await updateWorld(world.id, userId, { status: 'active' })
          refresh()
          break
          
        case 'delete':
          if (confirm(`Вы уверены, что хотите удалить мир "${world.name}"?`)) {
            await deleteWorld(world.id, userId)
            refresh()
          }
          break
          
        case 'toggle-public':
          await updateWorld(world.id, userId, { isPublic: !world.isPublic })
          refresh()
          break
      }
    } catch (err) {
      console.error('Failed to perform action:', err)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return mb < 1 ? `${Math.round(bytes / 1024)}KB` : `${mb.toFixed(1)}MB`
  }

  if (loading) {
    return <div className="worlds-list-loading">Загрузка миров...</div>
  }

  if (error) {
    return (
      <div className="worlds-list-error">
        <p>❌ Ошибка загрузки миров: {error}</p>
        <button onClick={refresh} className="btn btn-secondary">
          Попробовать снова
        </button>
      </div>
    )
  }

  return (
    <div className="worlds-list">
      <div className="worlds-list-header">
        <h2>Мои миры</h2>
        
        <div className="worlds-list-controls">
          <div className="status-filter">
            <label htmlFor="status-filter">Статус:</label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
            >
              <option value="active">Активные</option>
              <option value="archived">Архивированные</option>
              <option value="all">Все</option>
            </select>
          </div>
          
          <button onClick={refresh} className="btn btn-secondary">
            🔄 Обновить
          </button>
        </div>
      </div>

      {worlds.length === 0 ? (
        <div className="worlds-list-empty">
          <p>У вас пока нет миров.</p>
          <p>Создайте свой первый мир!</p>
        </div>
      ) : (
        <>
          <div className="worlds-grid">
            {worlds.map((world) => (
              <div 
                key={world.id} 
                className={`world-card ${selectedWorld === world.id ? 'selected' : ''}`}
                onClick={() => {
                  setSelectedWorld(world.id)
                  onWorldSelect?.(world.id)
                }}
              >
                <div className="world-card-header">
                  <h3 className="world-name">{world.name}</h3>
                  <div className="world-status">
                    <span className={`status-badge ${world.status}`}>
                      {world.status === 'active' ? 'Активный' : 'Архивирован'}
                    </span>
                    {world.isPublic && (
                      <span className="public-badge">Публичный</span>
                    )}
                  </div>
                </div>

                <div className="world-card-body">
                  <p className="world-description">{world.description}</p>
                  
                  <div className="world-info">
                    <div className="world-info-item">
                      <span className="label">Seed:</span>
                      <code className="seed">{world.seed}</code>
                    </div>
                    
                    <div className="world-info-item">
                      <span className="label">Размер:</span>
                      <span>{world.metadata.worldSize.width}×{world.metadata.worldSize.height}</span>
                    </div>
                    
                    <div className="world-info-item">
                      <span className="label">Сложность:</span>
                      <span>{world.metadata.difficulty}</span>
                    </div>
                    
                    <div className="world-info-item">
                      <span className="label">Алгоритм:</span>
                      <span>{world.metadata.algorithm}</span>
                    </div>
                  </div>

                  <div className="world-stats">
                    <div className="stat">
                      <span className="stat-value">{world.metadata.stats.locationsCount}</span>
                      <span className="stat-label">Локаций</span>
                    </div>
                    <div className="stat">
                      <span className="stat-value">{world.metadata.stats.npcsCount}</span>
                      <span className="stat-label">NPC</span>
                    </div>
                    <div className="stat">
                      <span className="stat-value">{world.metadata.stats.factionsCount}</span>
                      <span className="stat-label">Фракций</span>
                    </div>
                  </div>

                  <div className="world-meta">
                    <div className="world-meta-item">
                      <span className="label">Создан:</span>
                      <span>{formatDate(world.createdAt)}</span>
                    </div>
                    
                    <div className="world-meta-item">
                      <span className="label">Загружен:</span>
                      <span>{world.loadCount} раз</span>
                    </div>
                    
                    <div className="world-meta-item">
                      <span className="label">Размер:</span>
                      <span>{formatSize(world.sizeBytes)}</span>
                    </div>
                    
                    {world.lastLoadedAt && (
                      <div className="world-meta-item">
                        <span className="label">Последний раз:</span>
                        <span>{formatDate(world.lastLoadedAt)}</span>
                      </div>
                    )}
                  </div>

                  {world.metadata.tags.length > 0 && (
                    <div className="world-tags">
                      {world.metadata.tags.map((tag, index) => (
                        <span key={index} className="tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                <div className="world-card-actions">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleWorldAction('load', world)
                    }}
                    className="btn btn-primary"
                    disabled={managementLoading}
                  >
                    🎮 Загрузить
                  </button>
                  
                  <div className="world-actions-menu">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleWorldAction('toggle-public', world)
                      }}
                      className="btn btn-secondary"
                      disabled={managementLoading}
                      title={world.isPublic ? 'Сделать приватным' : 'Сделать публичным'}
                    >
                      {world.isPublic ? '🔓' : '🔒'}
                    </button>
                    
                    {world.status === 'active' ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleWorldAction('archive', world)
                        }}
                        className="btn btn-secondary"
                        disabled={managementLoading}
                        title="Архивировать"
                      >
                        📦
                      </button>
                    ) : (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleWorldAction('unarchive', world)
                        }}
                        className="btn btn-secondary"
                        disabled={managementLoading}
                        title="Восстановить"
                      >
                        📤
                      </button>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleWorldAction('delete', world)
                      }}
                      className="btn btn-danger"
                      disabled={managementLoading}
                      title="Удалить"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Пагинация */}
          {pagination.totalPages > 1 && (
            <div className="worlds-pagination">
              <button
                onClick={() => loadPage(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="btn btn-secondary"
              >
                ← Предыдущая
              </button>
              
              <span className="pagination-info">
                Страница {pagination.page} из {pagination.totalPages}
                ({pagination.total} миров)
              </span>
              
              <button
                onClick={() => loadPage(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className="btn btn-secondary"
              >
                Следующая →
              </button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
