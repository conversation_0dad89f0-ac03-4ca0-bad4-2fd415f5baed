import React from 'react'
import { useNavigate } from 'react-router-dom'
import MainMenu from '../components/MainMenu'
import { useGameStore } from '../store/gameStore'

const MainMenuPage: React.FC = () => {
  const navigate = useNavigate()
  const { setCurrentWorld } = useGameStore()

  const handleStartGame = (worldId: string) => {
    setCurrentWorld(worldId)
    navigate('/game')
  }

  return <MainMenu onStartGame={handleStartGame} />
}

export default MainMenuPage
