/* Global styles for NuclearStory */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0f0f0f;
  color: #ffffff;
  line-height: 1.6;
}

html, body, #root {
  height: 100%;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  font-family: inherit;
}

input, textarea {
  font-family: inherit;
  outline: none;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Nuclear theme colors */
:root {
  --nuclear-green: #00ff41;
  --nuclear-orange: #ff6b00;
  --nuclear-red: #ff0000;
  --nuclear-yellow: #ffff00;
  --dark-bg: #0f0f0f;
  --dark-card: #1a1a1a;
  --dark-border: #333333;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;
}

/* App styles */
.app {
  min-height: 100vh;
  background-color: var(--dark-bg);
  color: var(--text-primary);
}
