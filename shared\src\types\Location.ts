import { LocationType, TerrainType } from '../enums'
import { Position } from '../models/Player'

export interface LocationExit {
  id: string
  name: string
  description: string
  targetLocationId: string
  position: Position
  isLocked: boolean
  keyRequired?: string // Item ID
  skillRequired?: {
    skill: string
    level: number
  }
  isHidden: boolean
  discoveryRequirement?: string
}

export interface LocationContainer {
  id: string
  name: string
  description: string
  position: Position
  isLocked: boolean
  keyRequired?: string // Item ID
  skillRequired?: {
    skill: string
    level: number
  }
  contents: string[] // Item IDs
  isLooted: boolean
  respawnTime?: number // minutes
  lastLootedAt?: Date

  // Ссылка на полный контейнер (новая система)
  containerRef?: string // ID полного контейнера из Container типа
}

export interface LocationHazard {
  id: string
  type: 'radiation' | 'toxic' | 'fire' | 'electric' | 'trap'
  position: Position
  radius: number
  damage: number
  isActive: boolean
  canBeDisabled: boolean
  disableRequirement?: {
    skill: string
    level: number
    item?: string
  }
}

export interface LocationEvent {
  id: string
  name: string
  description: string
  triggerConditions: {
    playerLevel?: number
    questCompleted?: string
    itemInInventory?: string
    timeOfDay?: 'day' | 'night'
    visitCount?: number
  }
  isTriggered: boolean
  isRepeatable: boolean
  consequences: {
    spawnNPCs?: string[] // NPC IDs
    addItems?: string[] // Item IDs
    startQuest?: string // Quest ID
    changeReputation?: Record<string, number> // Faction ID -> change
  }
}

export interface Location {
  id: string
  name: string
  description: string
  type: LocationType
  
  // Map properties
  position: Position
  size: {
    width: number
    height: number
  }
  terrain: TerrainType
  
  // Discovery and access
  isDiscovered: boolean
  isVisible: boolean
  discoveryRequirement?: {
    quest?: string
    item?: string
    skill?: {
      name: string
      level: number
    }
  }
  
  // Environment
  radiationLevel: number
  temperature: number
  lightLevel: number // 0-100
  hasWater: boolean
  hasElectricity: boolean
  hasShelter: boolean
  
  // Safety and danger
  dangerLevel: number // 0-100
  enemySpawnRate: number
  safeZone: boolean
  
  // NPCs and population
  npcs: string[] // NPC IDs currently in location
  maxNPCs: number
  enemyTypes: string[] // Enemy type IDs that can spawn here
  
  // Containers and loot
  containers: LocationContainer[]
  lootTables: string[] // Loot table IDs
  
  // Exits and connections
  exits: LocationExit[]
  connectedLocations: string[] // Location IDs
  
  // Hazards and traps
  hazards: LocationHazard[]
  
  // Events and interactions
  events: LocationEvent[]
  
  // Fast travel
  canFastTravel: boolean
  fastTravelCost?: number
  
  // Ownership and control
  ownerId?: string // Player or faction ID
  factionControlled?: string // Faction ID
  
  // Resources
  availableResources: {
    type: string
    amount: number
    regenerationRate: number
    lastHarvestedAt?: Date
  }[]
  
  // Weather and time effects
  weatherEffects: {
    visibility: number
    movementSpeed: number
    combatAccuracy: number
  }
  
  // Visit tracking
  visitCount: number
  firstVisitAt?: Date
  lastVisitAt?: Date
  totalTimeSpent: number // minutes
  
  // Timestamps
  createdAt: Date
  lastUpdatedAt: Date
}
