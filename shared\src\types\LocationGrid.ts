import { Position } from '../models/Player'
import { TerrainType } from '../enums'

export interface LocationObject {
  id: string
  type: 'container' | 'door' | 'npc' | 'furniture' | 'decoration' | 'hazard' | 'interactive'
  name: string
  description?: string
  position: Position
  
  // Визуальные свойства
  sprite?: string
  model?: string
  rotation: number // 0-360 градусов
  scale: number
  
  // Физические свойства
  isBlocking: boolean // блокирует ли движение
  isInteractable: boolean
  hasCollision: boolean
  
  // Состояние
  isActive: boolean
  isVisible: boolean
  isDestroyed: boolean
  
  // Специфичные данные для типа объекта
  objectData?: Record<string, any>
  
  // Метаданные
  createdAt: Date
  lastInteractedAt?: Date
}

export interface LocationCell {
  // Координаты ячейки в сетке локации
  position: Position
  
  // Тип поверхности
  terrain: TerrainType
  floorType: string // 'concrete', 'wood', 'metal', 'dirt', 'carpet', etc.
  
  // Высота и уровень
  elevation: number
  level: number // этаж (0 = первый этаж, -1 = подвал, 1 = второй этаж)
  
  // Проходимость
  isPassable: boolean
  movementCost: number
  requiresClimbing: boolean
  requiresSwimming: boolean
  
  // Освещение
  lightLevel: number // 0-100
  lightSources: string[] // ID источников света
  
  // Объекты в ячейке
  objects: string[] // LocationObject IDs
  
  // Покрытие и материалы
  wallType?: string // если есть стена
  ceilingType?: string // тип потолка
  hasRoof: boolean
  
  // Условия окружающей среды
  temperature: number
  humidity: number
  radiationLevel: number
  airQuality: number // 0-100
  
  // Звуки и атмосфера
  ambientSounds: string[]
  soundLevel: number // уровень шума
  
  // Запахи и следы
  scents: Array<{
    type: string
    intensity: number
    age: number // минут
  }>
  
  tracks: Array<{
    creatureType: string
    direction: number // градусы
    age: number // минут
    size: number
  }>
  
  // Состояние ячейки
  isDamaged: boolean
  damageLevel: number // 0-100
  isOnFire: boolean
  isFlooded: boolean
  isFrozen: boolean
  
  // Видимость и исследование
  isVisible: boolean
  isExplored: boolean
  fogOfWar: boolean
  lastVisitedAt?: Date
  
  // Специальные свойства
  isSpawnPoint: boolean
  isExitPoint: boolean
  isLandmark: boolean
  
  // Метаданные
  lastUpdatedAt: Date
}

export interface LocationLevel {
  // Уровень (этаж)
  level: number
  name: string
  description?: string
  
  // Размеры уровня
  gridSize: {
    width: number
    height: number
  }
  
  // Сетка ячеек
  cells: LocationCell[][]
  
  // Объекты на уровне
  objects: Record<string, LocationObject>
  
  // Связи между уровнями
  connections: Array<{
    type: 'stairs' | 'ladder' | 'elevator' | 'ramp' | 'hole'
    position: Position
    targetLevel: number
    targetPosition: Position
    isBlocked: boolean
    requiresKey?: string
  }>
  
  // Освещение уровня
  globalLighting: {
    ambientLight: number
    hasElectricity: boolean
    powerSources: string[]
  }
  
  // Климат уровня
  climate: {
    temperature: number
    humidity: number
    airCirculation: number
    hasVentilation: boolean
  }
}

export interface LocationGrid {
  // ID локации
  locationId: string
  
  // Основные размеры
  totalSize: {
    width: number
    height: number
    levels: number
  }
  
  // Уровни (этажи)
  levels: Record<number, LocationLevel>
  
  // Глобальные объекты (многоуровневые)
  globalObjects: Record<string, LocationObject>
  
  // Системы локации
  systems: {
    // Электричество
    electrical: {
      hasPower: boolean
      powerLevel: number // 0-100
      generators: string[] // object IDs
      powerGrid: Array<{
        from: Position
        to: Position
        isActive: boolean
      }>
    }
    
    // Водоснабжение
    plumbing: {
      hasWater: boolean
      waterPressure: number // 0-100
      waterSources: string[] // object IDs
      pipes: Array<{
        from: Position
        to: Position
        isWorking: boolean
      }>
    }
    
    // Вентиляция
    ventilation: {
      airQuality: number // 0-100
      ventilationRate: number
      vents: string[] // object IDs
    }
    
    // Безопасность
    security: {
      alarmSystem: boolean
      cameras: string[] // object IDs
      locks: string[] // object IDs
      securityLevel: number // 0-100
    }
  }
  
  // Зоны локации
  zones: Array<{
    id: string
    name: string
    type: 'residential' | 'commercial' | 'industrial' | 'storage' | 'security' | 'medical' | 'recreational'
    bounds: {
      minX: number
      minY: number
      maxX: number
      maxY: number
      level: number
    }
    properties: Record<string, any>
  }>
  
  // Пути и навигация
  pathfinding: {
    walkableAreas: Array<{
      level: number
      bounds: {
        minX: number
        minY: number
        maxX: number
        maxY: number
      }
    }>
    
    blockedAreas: Array<{
      level: number
      bounds: {
        minX: number
        minY: number
        maxX: number
        maxY: number
      }
      reason: string
    }>
    
    shortcuts: Array<{
      from: Position
      to: Position
      cost: number
      requirements?: string[]
    }>
  }
  
  // Метаданные
  generatedAt: Date
  lastUpdatedAt: Date
  version: string
}
