import { MapCell } from './MapCell'
import { Location } from './Location'
import { NPC } from './NPC'
import { Position } from '../models/Player'

export interface WorldSettings {
  // Генерация мира
  seed: string
  generationAlgorithm: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'
  
  // Размеры мира
  worldSize: {
    width: number
    height: number
  }
  
  // Игровые настройки
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare'
  timeScale: number // множитель времени (1.0 = реальное время)
  dayDuration: number // минут в игровом дне
  
  // Системы мира
  weatherSystem: boolean
  seasonalChanges: boolean
  dynamicEconomy: boolean
  factionWars: boolean
  
  // Генерация контента
  locationDensity: number // 0-100, плотность локаций
  npcDensity: number // 0-100, плотность NPC
  resourceAbundance: number // 0-100, изобилие ресурсов
  dangerLevel: number // 0-100, общий уровень опасности
  
  // Радиация и окружающая среда
  globalRadiation: number // базовый уровень радиации
  radiationZones: boolean // есть ли зоны повышенной радиации
  mutationRate: number // частота мутаций
  
  // Экономика
  inflationRate: number // инфляция цен
  tradeRouteStability: number // стабильность торговых путей
  
  // События
  randomEventFrequency: number // частота случайных событий
  questGeneration: boolean // автогенерация квестов
  
  // Выживание
  hungerRate: number // скорость голода
  thirstRate: number // скорость жажды
  fatigueRate: number // скорость усталости
  healingRate: number // скорость восстановления здоровья
}

export interface WorldMetadata {
  // Основная информация
  name: string
  description: string
  version: string
  
  // Статистика
  totalLocations: number
  totalNPCs: number
  totalItems: number
  totalQuests: number
  
  // Прогресс генерации
  generationProgress: number // 0-100
  isFullyGenerated: boolean
  
  // Временные метки
  createdAt: Date
  lastUpdatedAt: Date
  lastPlayedAt?: Date
  
  // Игровая статистика
  totalPlaytime: number // минут
  playerDeaths: number
  questsCompleted: number
  locationsDiscovered: number
}

export interface WorldState {
  // Текущее время в мире
  currentTime: {
    day: number
    hour: number
    minute: number
    season: 'spring' | 'summer' | 'autumn' | 'winter'
  }
  
  // Глобальная погода
  weather: {
    temperature: number
    humidity: number
    windSpeed: number
    precipitation: number
    visibility: number
    radiationStorm: boolean
  }
  
  // Глобальные события
  activeEvents: string[] // Event IDs
  
  // Экономическое состояние
  economy: {
    globalInflation: number
    tradeRouteStatus: Record<string, 'active' | 'disrupted' | 'destroyed'>
    resourcePrices: Record<string, number>
  }
  
  // Состояние фракций
  factionRelations: Record<string, Record<string, number>> // faction1 -> faction2 -> relation (-100 to 100)
  territoryControl: Record<string, string> // locationId -> factionId
  
  // Глобальные флаги
  worldFlags: Record<string, boolean>
  
  // Счетчики
  counters: Record<string, number>
}

export interface World {
  // Основная информация
  id: string
  metadata: WorldMetadata
  settings: WorldSettings
  state: WorldState
  
  // Сетка мира (основная карта)
  worldGrid: MapCell[][]
  
  // Все объекты мира
  locations: Record<string, Location>
  npcs: Record<string, NPC>
  
  // Глобальные системы
  questSystem: {
    availableQuests: string[] // Quest IDs
    completedQuests: string[] // Quest IDs
    questTemplates: string[] // Quest template IDs
  }
  
  eventSystem: {
    scheduledEvents: Array<{
      eventId: string
      triggerTime: number // game time
      conditions?: Record<string, any>
    }>
    recurringEvents: Array<{
      eventId: string
      interval: number // game hours
      lastTriggered: number
    }>
  }
  
  // Системы генерации
  generators: {
    locationGenerator: {
      templates: string[]
      lastGenerated: Position
      generationQueue: Position[]
    }
    npcGenerator: {
      templates: string[]
      spawnPoints: Position[]
      maxNPCs: number
    }
    questGenerator: {
      templates: string[]
      difficulty: number
      themes: string[]
    }
  }
  
  // Кэш для оптимизации
  cache: {
    loadedChunks: Set<string> // "x,y" координаты загруженных чанков
    activeRegions: Set<string> // активные регионы для симуляции
    nearbyLocations: Record<string, string[]> // locationId -> nearby locationIds
  }
}

export interface WorldChunk {
  // Координаты чанка
  chunkX: number
  chunkY: number
  
  // Размер чанка
  chunkSize: number
  
  // Ячейки в чанке
  cells: MapCell[][]
  
  // Локации в чанке
  locationIds: string[]
  
  // NPC в чанке
  npcIds: string[]
  
  // Состояние чанка
  isLoaded: boolean
  isActive: boolean // активная симуляция
  lastAccessTime: Date
  
  // Генерация
  isGenerated: boolean
  generationSeed: string
}
