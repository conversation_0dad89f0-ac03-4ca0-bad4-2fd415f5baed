# 🪟 NuclearStory - Windows Setup Guide

## 🚀 Быстрый запуск на Windows

### Предварительные требования
1. **Docker Desktop** - обязательно установлен и запущен
2. **Git** - для клонирования репозитория

### 🎯 Способы запуска

#### 1. Простой запуск (рекомендуется)
```cmd
cd infra
start-dev.bat
```

#### 2. PowerShell (для продвинутых)
```powershell
cd infra
.\scripts\dev-start.ps1
```

#### 3. Bash (если установлен <PERSON>it <PERSON>sh)
```bash
cd infra
./scripts/dev-start.sh
```

### 🛑 Остановка

#### Простой способ:
```cmd
cd infra
stop-dev.bat
```

#### PowerShell:
```powershell
cd infra
.\scripts\dev-stop.ps1
```

### 📊 Мониторинг

#### Просмотр логов:
```cmd
cd infra
docker compose -f docker-compose.dev.yml logs -f
```

#### Статус сервисов:
```cmd
cd infra
docker compose -f docker-compose.dev.yml ps
```

#### PowerShell мониторинг:
```powershell
cd infra
.\scripts\dev-logs.ps1
.\scripts\status.ps1
```

## 🌐 Доступ к приложению

После запуска будут доступны:

- **🎮 Игра**: http://localhost:3000
- **🌍 API Gateway**: http://localhost:80
- **📚 API Документация**: 
  - Auth: http://localhost:3001/api/docs
  - Game Engine: http://localhost:3002/api/docs
  - Story: http://localhost:3003/api/docs
  - Save: http://localhost:3004/api/docs
  - AI: http://localhost:3005/docs

## 🔥 Hot Reload

Все изменения в коде автоматически применяются:
- **Frontend** (React + Vite) - мгновенная перезагрузка
- **Backend** (NestJS) - автоматический перезапуск
- **AI Service** (Python) - автоматический перезапуск

Просто редактируйте файлы в папках `front/`, `back/`, `ai/` и видите изменения!

## 🐛 Решение проблем

### Docker не запускается
1. Убедитесь, что Docker Desktop запущен
2. Проверьте, что WSL2 включен (для Windows 10/11)

### Порты заняты
Если порты 3000, 80, 3001-3005 заняты:
```cmd
netstat -ano | findstr :3000
taskkill /PID <PID> /F
```

### Очистка Docker
Если что-то пошло не так:
```cmd
cd infra
docker compose -f docker-compose.dev.yml down -v
docker system prune -f
```

### Пересборка контейнеров
```cmd
cd infra
docker compose -f docker-compose.dev.yml down
docker compose -f docker-compose.dev.yml build --no-cache
start-dev.bat
```

## 💡 Полезные команды

```cmd
# Просмотр логов конкретного сервиса
docker compose -f docker-compose.dev.yml logs -f auth-service

# Вход в контейнер
docker compose -f docker-compose.dev.yml exec auth-service bash

# Перезапуск сервиса
docker compose -f docker-compose.dev.yml restart auth-service

# Просмотр использования ресурсов
docker stats
```

## 🎯 Что дальше?

1. Откройте http://localhost:3000 - увидите игру
2. Начните редактировать код в любой папке
3. Изменения применятся автоматически
4. Используйте API документацию для понимания эндпоинтов

Удачной разработки! 🚀
