import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export interface World {
  id: string
  name: string
  createdAt: Date
  lastPlayed: Date
  progress: number
  description?: string
  seed?: string
  worldId?: string // ID мира в базе данных
}

// API функции для работы с мирами
const API_BASE = '/api'

interface CreateWorldParams {
  name: string
  description?: string
  seed: string
  userId: string
  worldWidth?: number
  worldHeight?: number
  algorithm?: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'
  difficulty?: 'easy' | 'normal' | 'hard' | 'nightmare'
  generateWithAI?: boolean
}

const worldApi = {
  async createWorld(params: CreateWorldParams): Promise<{ worldId: string; status: string }> {
    const response = await fetch(`${API_BASE}/world-generation/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        seed: params.seed,
        userId: params.userId,
        worldWidth: params.worldWidth || 100,
        worldHeight: params.worldHeight || 100,
        algorithm: params.algorithm || 'hybrid',
        difficulty: params.difficulty || 'normal',
        locationDensity: 50,
        npcDensity: 40,
        resourceAbundance: 60,
        dangerLevel: 45,
        globalRadiation: 25,
        radiationZones: true,
        weatherSystem: true,
        seasonalChanges: false,
        dynamicEconomy: true,
        factionWars: true,
        generateWithAI: params.generateWithAI || true,
        timeScale: 1.0,
        dayDuration: 24
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to create world: ${response.statusText}`)
    }

    return response.json()
  },

  async getGenerationStatus(worldId: string): Promise<any> {
    const response = await fetch(`${API_BASE}/world-generation/status/${worldId}`)

    if (!response.ok) {
      throw new Error(`Failed to get generation status: ${response.statusText}`)
    }

    return response.json()
  },

  async getWorld(worldId: string, userId?: string): Promise<any> {
    const url = new URL(`${API_BASE}/world-generation/world/${worldId}`, window.location.origin)
    if (userId) {
      url.searchParams.set('userId', userId)
    }

    const response = await fetch(url.toString())

    if (!response.ok) {
      throw new Error(`Failed to get world: ${response.statusText}`)
    }

    const data = await response.json()
    return data.world
  }
}

export interface GameSettings {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: 'easy' | 'normal' | 'hard'
  autoSave: boolean
}

interface GameState {
  worlds: World[]
  settings: GameSettings
  currentWorldId: string | null

  // World creation state
  isCreatingWorld: boolean
  creationProgress: number
  creationStatus: string
  creationError: string | null

  // Actions
  addWorld: (world: Omit<World, 'id' | 'createdAt' | 'lastPlayed'>) => void
  createWorldOnServer: (params: CreateWorldParams) => Promise<void>
  deleteWorld: (worldId: string) => void
  updateWorld: (worldId: string, updates: Partial<World>) => void
  setCurrentWorld: (worldId: string | null) => void
  updateSettings: (settings: Partial<GameSettings>) => void
  loadWorld: (worldId: string) => void
  resetCreationState: () => void
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      worlds: [],
      settings: defaultSettings,
      currentWorldId: null,

      // World creation state
      isCreatingWorld: false,
      creationProgress: 0,
      creationStatus: '',
      creationError: null,

      addWorld: (worldData) => {
        const newWorld: World = {
          ...worldData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          lastPlayed: new Date(),
          progress: 0
        }

        set((state) => ({
          worlds: [...state.worlds, newWorld]
        }))
      },

      createWorldOnServer: async (params) => {
        try {
          set({
            isCreatingWorld: true,
            creationProgress: 0,
            creationStatus: 'Отправка запроса на создание мира...',
            creationError: null
          })

          // Создаем мир на сервере
          const result = await worldApi.createWorld(params)

          set({
            creationProgress: 10,
            creationStatus: 'Мир создается на сервере...'
          })

          // Отслеживаем прогресс генерации
          const checkProgress = async () => {
            try {
              const status = await worldApi.getGenerationStatus(result.worldId)

              set({
                creationProgress: status.progress || 0,
                creationStatus: status.currentTask || 'Генерация мира...'
              })

              if (status.status === 'completed') {
                // Мир создан успешно, добавляем в локальный store
                const newWorld: World = {
                  id: crypto.randomUUID(),
                  name: params.name,
                  description: params.description,
                  seed: params.seed,
                  worldId: result.worldId, // ID мира в базе данных
                  createdAt: new Date(),
                  lastPlayed: new Date(),
                  progress: 0
                }

                set((state) => ({
                  worlds: [...state.worlds, newWorld],
                  isCreatingWorld: false,
                  creationProgress: 100,
                  creationStatus: 'Мир успешно создан!'
                }))

              } else if (status.status === 'failed') {
                set({
                  isCreatingWorld: false,
                  creationError: status.error || 'Ошибка создания мира'
                })
              } else {
                // Продолжаем проверять через 2 секунды
                setTimeout(checkProgress, 2000)
              }
            } catch (error) {
              set({
                isCreatingWorld: false,
                creationError: error instanceof Error ? error.message : 'Ошибка проверки статуса'
              })
            }
          }

          // Начинаем проверку через 1 секунду
          setTimeout(checkProgress, 1000)

        } catch (error) {
          set({
            isCreatingWorld: false,
            creationError: error instanceof Error ? error.message : 'Ошибка создания мира'
          })
        }
      },

      resetCreationState: () => {
        set({
          isCreatingWorld: false,
          creationProgress: 0,
          creationStatus: '',
          creationError: null
        })
      },

      deleteWorld: (worldId) => {
        set((state) => ({
          worlds: state.worlds.filter(world => world.id !== worldId),
          currentWorldId: state.currentWorldId === worldId ? null : state.currentWorldId
        }))
      },

      updateWorld: (worldId, updates) => {
        set((state) => ({
          worlds: state.worlds.map(world =>
            world.id === worldId
              ? { ...world, ...updates, lastPlayed: new Date() }
              : world
          )
        }))
      },

      setCurrentWorld: (worldId) => {
        set({ currentWorldId: worldId })
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      loadWorld: (worldId) => {
        const { updateWorld, setCurrentWorld } = get()
        updateWorld(worldId, { lastPlayed: new Date() })
        setCurrentWorld(worldId)
      }
    }),
    {
      name: 'nuclear-story-game-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        worlds: state.worlds,
        settings: state.settings,
        currentWorldId: state.currentWorldId
      })
    }
  )
)
