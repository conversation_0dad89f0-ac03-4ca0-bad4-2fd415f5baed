import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  HttpCode, 
  HttpStatus 
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger'
import { WorldsService } from './worlds.service'
import { SaveWorldDto, UpdateWorldDto, WorldSummaryDto } from './dto/worlds.dto'

/**
 * Контроллер для управления мирами
 */
@ApiTags('Worlds')
@Controller('worlds')
export class WorldsController {
  constructor(private readonly worldsService: WorldsService) {}

  /**
   * Сохраняет сгенерированный мир
   */
  @Post()
  @ApiOperation({ summary: 'Save a generated world' })
  @ApiResponse({ status: 201, description: 'World saved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid world data' })
  async saveWorld(@Body() saveWorldDto: SaveWorldDto) {
    const savedWorld = await this.worldsService.saveWorld(
      saveWorldDto.userId,
      saveWorldDto.worldData,
      saveWorldDto.generationTime,
      saveWorldDto.generatedWithAI
    )

    return {
      success: true,
      worldId: savedWorld.id,
      message: 'World saved successfully'
    }
  }

  /**
   * Получает мир по ID
   */
  @Get(':worldId')
  @ApiOperation({ summary: 'Get world by ID' })
  @ApiResponse({ status: 200, description: 'World retrieved successfully' })
  @ApiResponse({ status: 404, description: 'World not found' })
  async getWorld(
    @Param('worldId') worldId: string,
    @Query('userId') userId?: string
  ): Promise<any> {
    const world = await this.worldsService.getWorldById(worldId, userId)
    
    return {
      success: true,
      world: world.worldData,
      metadata: {
        id: world.id,
        name: world.name,
        description: world.description,
        seed: world.seed,
        loadCount: world.loadCount,
        lastLoadedAt: world.lastLoadedAt,
        createdAt: world.createdAt,
        stats: world.metadata?.stats
      }
    }
  }

  /**
   * Получает краткую информацию о мире (без полных данных)
   */
  @Get(':worldId/summary')
  @ApiOperation({ summary: 'Get world summary' })
  @ApiResponse({ status: 200, description: 'World summary retrieved successfully' })
  async getWorldSummary(
    @Param('worldId') worldId: string,
    @Query('userId') userId?: string
  ) {
    const world = await this.worldsService.getWorldById(worldId, userId)
    
    return {
      success: true,
      summary: world.toSummary()
    }
  }

  /**
   * Получает список миров пользователя
   */
  @Get('user/:userId')
  @ApiOperation({ summary: 'Get user worlds' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: ['active', 'archived', 'all'] })
  @ApiResponse({ status: 200, description: 'User worlds retrieved successfully' })
  async getUserWorlds(
    @Param('userId') userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status: 'active' | 'archived' | 'all' = 'active'
  ) {
    const result = await this.worldsService.getUserWorlds(userId, page, limit, status)
    
    return {
      success: true,
      worlds: result.worlds.map(world => world.toSummary()),
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: Math.ceil(result.total / result.limit)
      }
    }
  }

  /**
   * Получает публичные миры
   */
  @Get('public/list')
  @ApiOperation({ summary: 'Get public worlds' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Public worlds retrieved successfully' })
  async getPublicWorlds(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string
  ) {
    const result = await this.worldsService.getPublicWorlds(page, limit, search)
    
    return {
      success: true,
      worlds: result.worlds.map(world => world.toSummary()),
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: Math.ceil(result.total / result.limit)
      }
    }
  }

  /**
   * Поиск миров по seed
   */
  @Get('search/seed/:seed')
  @ApiOperation({ summary: 'Find worlds by seed' })
  @ApiResponse({ status: 200, description: 'Worlds found successfully' })
  async findWorldsBySeed(@Param('seed') seed: string) {
    const worlds = await this.worldsService.findWorldsBySeed(seed)
    
    return {
      success: true,
      worlds: worlds.map(world => world.toSummary()),
      count: worlds.length
    }
  }

  /**
   * Обновляет информацию о мире
   */
  @Put(':worldId')
  @ApiOperation({ summary: 'Update world information' })
  @ApiResponse({ status: 200, description: 'World updated successfully' })
  @ApiResponse({ status: 404, description: 'World not found' })
  async updateWorld(
    @Param('worldId') worldId: string,
    @Body() updateWorldDto: UpdateWorldDto
  ) {
    const updatedWorld = await this.worldsService.updateWorld(
      worldId,
      updateWorldDto.userId,
      updateWorldDto
    )
    
    return {
      success: true,
      world: updatedWorld.toSummary(),
      message: 'World updated successfully'
    }
  }

  /**
   * Архивирует мир
   */
  @Put(':worldId/archive')
  @ApiOperation({ summary: 'Archive world' })
  @ApiResponse({ status: 200, description: 'World archived successfully' })
  @HttpCode(HttpStatus.OK)
  async archiveWorld(
    @Param('worldId') worldId: string,
    @Body('userId') userId: string
  ) {
    await this.worldsService.archiveWorld(worldId, userId)
    
    return {
      success: true,
      message: 'World archived successfully'
    }
  }

  /**
   * Удаляет мир
   */
  @Delete(':worldId')
  @ApiOperation({ summary: 'Delete world' })
  @ApiResponse({ status: 200, description: 'World deleted successfully' })
  @HttpCode(HttpStatus.OK)
  async deleteWorld(
    @Param('worldId') worldId: string,
    @Query('userId') userId: string
  ) {
    await this.worldsService.deleteWorld(worldId, userId)
    
    return {
      success: true,
      message: 'World deleted successfully'
    }
  }

  /**
   * Получает статистику миров пользователя
   */
  @Get('user/:userId/stats')
  @ApiOperation({ summary: 'Get user worlds statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getUserWorldsStats(@Param('userId') userId: string) {
    const stats = await this.worldsService.getUserWorldsStats(userId)
    
    return {
      success: true,
      stats
    }
  }

  /**
   * Получает общую статистику хранилища (админ)
   */
  @Get('admin/storage-stats')
  @ApiOperation({ summary: 'Get storage statistics' })
  @ApiResponse({ status: 200, description: 'Storage statistics retrieved successfully' })
  async getStorageStats() {
    const stats = await this.worldsService.getStorageStats()
    
    return {
      success: true,
      stats: {
        ...stats,
        totalSizeMB: Math.round(stats.totalSizeBytes / 1024 / 1024 * 100) / 100,
        averageSizeMB: Math.round(stats.averageSizeBytes / 1024 / 1024 * 100) / 100,
        largestWorldSizeMB: Math.round(stats.largestWorldSize / 1024 / 1024 * 100) / 100
      }
    }
  }
}
