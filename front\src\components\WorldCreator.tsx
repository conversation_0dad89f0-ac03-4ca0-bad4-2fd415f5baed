import React, { useState } from 'react'
import { useWorldCreation } from '../hooks/useWorlds'
import { CreateWorldRequest } from '../api/worldsApi'

interface WorldCreatorProps {
  userId: string
  onWorldCreated?: (worldId: string) => void
}

export const WorldCreator: React.FC<WorldCreatorProps> = ({ userId, onWorldCreated }) => {
  const { createWorld, isCreating, worldId, status, error, reset, isCompleted } = useWorldCreation()
  
  const [formData, setFormData] = useState<CreateWorldRequest>({
    seed: '',
    userId,
    worldWidth: 100,
    worldHeight: 100,
    algorithm: 'hybrid',
    difficulty: 'normal',
    locationDensity: 50,
    npcDensity: 40,
    resourceAbundance: 60,
    dangerLevel: 45,
    globalRadiation: 25,
    radiationZones: true,
    weatherSystem: true,
    seasonalChanges: false,
    dynamicEconomy: true,
    factionWars: true,
    generateWithAI: true,
    timeScale: 1.0,
    dayDuration: 24
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.seed.trim()) {
      alert('Пожалуйста, введите seed для мира')
      return
    }

    await createWorld(formData)
  }

  const handleInputChange = (field: keyof CreateWorldRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Если мир создан успешно
  if (isCompleted && worldId) {
    return (
      <div className="world-creator-success">
        <h2>🎉 Мир успешно создан!</h2>
        <p>ID мира: <code>{worldId}</code></p>
        <p>Seed: <code>{formData.seed}</code></p>
        
        {status?.stats && (
          <div className="world-stats">
            <h3>Статистика мира:</h3>
            <ul>
              <li>Локаций: {status.stats.locationsGenerated}</li>
              <li>NPC: {status.stats.npcsGenerated}</li>
              <li>Фракций: {status.stats.factionsGenerated}</li>
              <li>Ячеек карты: {status.stats.cellsGenerated}</li>
            </ul>
          </div>
        )}
        
        <div className="actions">
          <button 
            onClick={() => onWorldCreated?.(worldId)}
            className="btn btn-primary"
          >
            Загрузить мир
          </button>
          <button 
            onClick={reset}
            className="btn btn-secondary"
          >
            Создать новый мир
          </button>
        </div>
      </div>
    )
  }

  // Если идет создание мира
  if (isCreating && status) {
    return (
      <div className="world-creator-progress">
        <h2>Создание мира...</h2>
        <div className="progress-info">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${status.progress}%` }}
            />
          </div>
          <p className="progress-text">{status.progress}%</p>
        </div>
        
        <div className="current-task">
          <p><strong>Этап:</strong> {status.currentStage}</p>
          <p><strong>Задача:</strong> {status.currentTask}</p>
          {status.estimatedTimeRemaining && (
            <p><strong>Осталось:</strong> ~{status.estimatedTimeRemaining}с</p>
          )}
        </div>
      </div>
    )
  }

  // Форма создания мира
  return (
    <div className="world-creator">
      <h2>Создать новый мир</h2>
      
      {error && (
        <div className="error-message">
          <p>❌ Ошибка: {error}</p>
          <button onClick={reset} className="btn btn-secondary">
            Попробовать снова
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="world-form">
        {/* Основные параметры */}
        <div className="form-section">
          <h3>Основные параметры</h3>
          
          <div className="form-group">
            <label htmlFor="seed">Seed мира:</label>
            <input
              id="seed"
              type="text"
              value={formData.seed}
              onChange={(e) => handleInputChange('seed', e.target.value)}
              placeholder="nuclear_wasteland_2077"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="worldWidth">Ширина мира:</label>
              <input
                id="worldWidth"
                type="number"
                min="50"
                max="500"
                value={formData.worldWidth}
                onChange={(e) => handleInputChange('worldWidth', parseInt(e.target.value))}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="worldHeight">Высота мира:</label>
              <input
                id="worldHeight"
                type="number"
                min="50"
                max="500"
                value={formData.worldHeight}
                onChange={(e) => handleInputChange('worldHeight', parseInt(e.target.value))}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="algorithm">Алгоритм генерации:</label>
              <select
                id="algorithm"
                value={formData.algorithm}
                onChange={(e) => handleInputChange('algorithm', e.target.value)}
              >
                <option value="hybrid">Гибридный (рекомендуется)</option>
                <option value="perlin">Perlin Noise</option>
                <option value="cellular">Cellular Automata</option>
                <option value="voronoi">Voronoi Diagrams</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="difficulty">Сложность:</label>
              <select
                id="difficulty"
                value={formData.difficulty}
                onChange={(e) => handleInputChange('difficulty', e.target.value)}
              >
                <option value="easy">Легкая</option>
                <option value="normal">Нормальная</option>
                <option value="hard">Сложная</option>
                <option value="nightmare">Кошмар</option>
              </select>
            </div>
          </div>
        </div>

        {/* Плотность объектов */}
        <div className="form-section">
          <h3>Плотность объектов</h3>
          
          <div className="form-group">
            <label htmlFor="locationDensity">Плотность локаций: {formData.locationDensity}%</label>
            <input
              id="locationDensity"
              type="range"
              min="0"
              max="100"
              value={formData.locationDensity}
              onChange={(e) => handleInputChange('locationDensity', parseInt(e.target.value))}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="npcDensity">Плотность NPC: {formData.npcDensity}%</label>
            <input
              id="npcDensity"
              type="range"
              min="0"
              max="100"
              value={formData.npcDensity}
              onChange={(e) => handleInputChange('npcDensity', parseInt(e.target.value))}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="dangerLevel">Уровень опасности: {formData.dangerLevel}%</label>
            <input
              id="dangerLevel"
              type="range"
              min="0"
              max="100"
              value={formData.dangerLevel}
              onChange={(e) => handleInputChange('dangerLevel', parseInt(e.target.value))}
            />
          </div>
        </div>

        {/* Системы мира */}
        <div className="form-section">
          <h3>Системы мира</h3>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={formData.radiationZones}
                onChange={(e) => handleInputChange('radiationZones', e.target.checked)}
              />
              Зоны радиации
            </label>
          </div>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={formData.weatherSystem}
                onChange={(e) => handleInputChange('weatherSystem', e.target.checked)}
              />
              Система погоды
            </label>
          </div>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={formData.dynamicEconomy}
                onChange={(e) => handleInputChange('dynamicEconomy', e.target.checked)}
              />
              Динамическая экономика
            </label>
          </div>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={formData.factionWars}
                onChange={(e) => handleInputChange('factionWars', e.target.checked)}
              />
              Войны фракций
            </label>
          </div>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={formData.generateWithAI}
                onChange={(e) => handleInputChange('generateWithAI', e.target.checked)}
              />
              Использовать AI для генерации
            </label>
          </div>
        </div>

        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={isCreating}
          >
            {isCreating ? 'Создание...' : 'Создать мир'}
          </button>
        </div>
      </form>
    </div>
  )
}
