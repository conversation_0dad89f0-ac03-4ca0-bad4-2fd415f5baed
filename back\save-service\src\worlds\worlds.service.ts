import { Injectable, NotFoundException, Logger } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { WorldEntity } from '../entities/world.entity'
// Временный тип World
interface World {
  id: string
  name: string
  seed: string
  userId: string
  worldData: any
  createdAt: string
  updatedAt: string
}

/**
 * Сервис для управления мирами в базе данных
 */
@Injectable()
export class WorldsService {
  private readonly logger = new Logger(WorldsService.name)

  constructor(
    @InjectRepository(WorldEntity)
    private readonly worldRepository: Repository<WorldEntity>
  ) {}

  /**
   * Сохраняет сгенерированный мир в базу данных
   */
  async saveWorld(
    userId: string, 
    world: World, 
    generationTime: number = 0,
    generatedWithAI: boolean = false
  ): Promise<WorldEntity> {
    this.logger.log(`Saving world ${world.id} for user ${userId}`)

    const worldEntity = new WorldEntity()
    worldEntity.userId = userId
    const data = world as any
    worldEntity.seed = data.settings?.seed || 'default'
    worldEntity.name = data.metadata?.name || 'Unnamed World'
    worldEntity.description = data.metadata?.description || 'No description'
    worldEntity.worldData = world

    // Обновляем метаданные
    worldEntity.updateMetadata()
    if (worldEntity.metadata) {
      worldEntity.metadata.generationTime = generationTime
      worldEntity.metadata.generatedWithAI = generatedWithAI
    }

    const savedWorld = await this.worldRepository.save(worldEntity)
    this.logger.log(`World ${world.id} saved with database ID ${savedWorld.id}`)

    return savedWorld
  }

  /**
   * Получает мир по ID
   */
  async getWorldById(worldId: string, userId?: string): Promise<WorldEntity> {
    const query = this.worldRepository.createQueryBuilder('world')
      .where('world.id = :worldId', { worldId })
      .andWhere('world.status != :status', { status: 'deleted' })

    // Если указан userId, проверяем права доступа
    if (userId) {
      query.andWhere('(world.userId = :userId OR world.isPublic = true)', { userId })
    }

    const world = await query.getOne()

    if (!world) {
      throw new NotFoundException(`World with ID ${worldId} not found`)
    }

    // Увеличиваем счетчик загрузок
    await this.incrementLoadCount(worldId)

    return world
  }

  /**
   * Получает список миров пользователя
   */
  async getUserWorlds(
    userId: string, 
    page: number = 1, 
    limit: number = 10,
    status: 'active' | 'archived' | 'all' = 'active'
  ): Promise<{ worlds: WorldEntity[]; total: number; page: number; limit: number }> {
    const query = this.worldRepository.createQueryBuilder('world')
      .where('world.userId = :userId', { userId })
      .orderBy('world.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)

    if (status !== 'all') {
      query.andWhere('world.status = :status', { status })
    } else {
      query.andWhere('world.status != :deletedStatus', { deletedStatus: 'deleted' })
    }

    const [worlds, total] = await query.getManyAndCount()

    return {
      worlds,
      total,
      page,
      limit
    }
  }

  /**
   * Получает публичные миры
   */
  async getPublicWorlds(
    page: number = 1, 
    limit: number = 10,
    search?: string
  ): Promise<{ worlds: WorldEntity[]; total: number; page: number; limit: number }> {
    const query = this.worldRepository.createQueryBuilder('world')
      .where('world.isPublic = true')
      .andWhere('world.status = :status', { status: 'active' })
      .orderBy('world.loadCount', 'DESC')
      .addOrderBy('world.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)

    if (search) {
      query.andWhere(
        '(world.name ILIKE :search OR world.description ILIKE :search)',
        { search: `%${search}%` }
      )
    }

    const [worlds, total] = await query.getManyAndCount()

    return {
      worlds,
      total,
      page,
      limit
    }
  }

  /**
   * Поиск миров по seed
   */
  async findWorldsBySeed(seed: string): Promise<WorldEntity[]> {
    return this.worldRepository.find({
      where: { 
        seed,
        status: 'active'
      },
      order: { createdAt: 'DESC' }
    })
  }

  /**
   * Обновляет мир
   */
  async updateWorld(
    worldId: string, 
    userId: string, 
    updates: Partial<Pick<WorldEntity, 'name' | 'description' | 'isPublic' | 'status'>>
  ): Promise<WorldEntity> {
    const world = await this.worldRepository.findOne({
      where: { 
        id: worldId, 
        userId,
        status: 'active'
      }
    })

    if (!world) {
      throw new NotFoundException(`World with ID ${worldId} not found`)
    }

    Object.assign(world, updates)
    return this.worldRepository.save(world)
  }

  /**
   * Архивирует мир
   */
  async archiveWorld(worldId: string, userId: string): Promise<void> {
    await this.updateWorld(worldId, userId, { status: 'archived' })
    this.logger.log(`World ${worldId} archived by user ${userId}`)
  }

  /**
   * Удаляет мир (мягкое удаление)
   */
  async deleteWorld(worldId: string, userId: string): Promise<void> {
    await this.updateWorld(worldId, userId, { status: 'deleted' })
    this.logger.log(`World ${worldId} deleted by user ${userId}`)
  }

  /**
   * Получает статистику миров пользователя
   */
  async getUserWorldsStats(userId: string): Promise<{
    totalWorlds: number
    activeWorlds: number
    archivedWorlds: number
    totalLoadCount: number
    averageWorldSize: number
  }> {
    const stats = await this.worldRepository
      .createQueryBuilder('world')
      .select([
        'COUNT(*) as totalWorlds',
        'SUM(CASE WHEN world.status = \'active\' THEN 1 ELSE 0 END) as activeWorlds',
        'SUM(CASE WHEN world.status = \'archived\' THEN 1 ELSE 0 END) as archivedWorlds',
        'SUM(world.loadCount) as totalLoadCount',
        'AVG(world.sizeBytes) as averageWorldSize'
      ])
      .where('world.userId = :userId', { userId })
      .andWhere('world.status != :status', { status: 'deleted' })
      .getRawOne()

    return {
      totalWorlds: parseInt(stats.totalWorlds) || 0,
      activeWorlds: parseInt(stats.activeWorlds) || 0,
      archivedWorlds: parseInt(stats.archivedWorlds) || 0,
      totalLoadCount: parseInt(stats.totalLoadCount) || 0,
      averageWorldSize: parseFloat(stats.averageWorldSize) || 0
    }
  }

  /**
   * Увеличивает счетчик загрузок мира
   */
  private async incrementLoadCount(worldId: string): Promise<void> {
    await this.worldRepository
      .createQueryBuilder()
      .update(WorldEntity)
      .set({ 
        loadCount: () => 'loadCount + 1',
        lastLoadedAt: new Date()
      })
      .where('id = :worldId', { worldId })
      .execute()
  }

  /**
   * Очищает старые удаленные миры (для cron задачи)
   */
  async cleanupDeletedWorlds(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    const result = await this.worldRepository
      .createQueryBuilder()
      .delete()
      .from(WorldEntity)
      .where('status = :status', { status: 'deleted' })
      .andWhere('updatedAt < :cutoffDate', { cutoffDate })
      .execute()

    this.logger.log(`Cleaned up ${result.affected} deleted worlds older than ${olderThanDays} days`)
    return result.affected || 0
  }

  /**
   * Получает статистику использования хранилища
   */
  async getStorageStats(): Promise<{
    totalWorlds: number
    totalSizeBytes: number
    averageSizeBytes: number
    largestWorldSize: number
  }> {
    const stats = await this.worldRepository
      .createQueryBuilder('world')
      .select([
        'COUNT(*) as totalWorlds',
        'SUM(world.sizeBytes) as totalSizeBytes',
        'AVG(world.sizeBytes) as averageSizeBytes',
        'MAX(world.sizeBytes) as largestWorldSize'
      ])
      .where('world.status != :status', { status: 'deleted' })
      .getRawOne()

    return {
      totalWorlds: parseInt(stats.totalWorlds) || 0,
      totalSizeBytes: parseInt(stats.totalSizeBytes) || 0,
      averageSizeBytes: parseFloat(stats.averageSizeBytes) || 0,
      largestWorldSize: parseInt(stats.largestWorldSize) || 0
    }
  }
}
