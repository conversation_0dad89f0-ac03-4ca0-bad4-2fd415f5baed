import { InventoryItem } from '../models/InventoryItem';
import { ItemType, ItemRarity } from '../enums';

export const CONSUMABLES: InventoryItem[] = [
  {
    id: 'stimpak',
    name: 'Стимулятор',
    description: 'Медицинский стимулятор довоенного производства. Мгновенно восстанавливает здоровье.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 75,
    stackable: true,
    maxStack: 10,
    effects: [
      {
        type: 'heal',
        value: 50,
        duration: 0,
        description: 'Восстанавливает 50 ОЗ'
      }
    ],
    consumeOnUse: true,
    usageTime: 3,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чудо довоенной медицины. Каждый житель пустоши мечтает найти стимулятор.',
    createdAt: new Date(),
  },

  {
    id: 'rad-away',
    name: 'Антирадин',
    description: 'Антирадиационный препарат. Выводит радиацию из организма.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.1,
    value: 120,
    stackable: true,
    maxStack: 10,
    effects: [
      {
        type: 'remove_radiation',
        value: 100,
        duration: 0,
        description: 'Убирает 100 рад'
      }
    ],
    consumeOnUse: true,
    usageTime: 5,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Жизненно важное лекарство в мире, отравленном радиацией.',
    createdAt: new Date(),
  },

  {
    id: 'nuka-cola',
    name: 'Ядер-Кола',
    description: 'Довоенный газированный напиток. Содержит кофеин и радиоактивные изотопы.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 20,
    stackable: true,
    maxStack: 20,
    effects: [
      {
        type: 'heal',
        value: 10,
        duration: 0,
        description: 'Восстанавливает 10 ОЗ'
      },
      {
        type: 'action_points',
        value: 20,
        duration: 300,
        description: '+20 ОД на 5 минут'
      },
      {
        type: 'radiation',
        value: 3,
        duration: 0,
        description: '+3 рад'
      }
    ],
    consumeOnUse: true,
    usageTime: 2,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Культовый напиток довоенной Америки. Теперь слегка радиоактивный.',
    createdAt: new Date(),
  },

  {
    id: 'dirty-water',
    name: 'Грязная вода',
    description: 'Бережно процежено через несколько слоев ткани',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 15,
    stackable: true,
    maxStack: 20,
    effects: [
      {
        type: 'heal',
        value: 15,
        duration: 0,
        description: 'Восстанавливает 15 ОЗ'
      },
      {
        type: 'thirst',
        value: -50,
        duration: 0,
        description: 'Утоляет жажду'
      }, 
      {
        type: 'radiation',
        value: 10,
        duration: 0,
        description: '+10 рад'
      }
    ],
    consumeOnUse: true,
    usageTime: 3,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Зажать нос перед употрелением',
    createdAt: new Date(),
  }
,
  {
    id: 'purified-water',
    name: 'Очищенная вода',
    description: 'Чистая питьевая вода. Бесценный ресурс в пустоши.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 15,
    stackable: true,
    maxStack: 20,
    effects: [
      {
        type: 'heal',
        value: 15,
        duration: 0,
        description: 'Восстанавливает 15 ОЗ'
      },
      {
        type: 'thirst',
        value: -50,
        duration: 0,
        description: 'Утоляет жажду'
      }
    ],
    consumeOnUse: true,
    usageTime: 3,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чистая вода - роскошь в мире после ядерной войны.',
    createdAt: new Date(),
  }
];

export const TOOLS: InventoryItem[] = [
  {
    id: 'lockpick',
    name: 'Отмычка',
    description: 'Простая металлическая отмычка для вскрытия замков.',
    type: ItemType.TOOL,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 5,
    stackable: true,
    maxStack: 50,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Незаменимый инструмент для любого исследователя пустоши.',
    createdAt: new Date(),
  },

  {
    id: 'crowbar',
    name: 'Лом',
    description: 'Тяжелый металлический лом. Может использоваться как оружие или инструмент.',
    type: ItemType.TOOL,
    rarity: ItemRarity.COMMON,
    weight: 3.0,
    value: 25,
    stackable: false,
    maxStack: 1,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Универсальный инструмент для вскрытия ящиков и самообороны.',
    createdAt: new Date(),
  },

];

export const RESOURCES: InventoryItem[] = [
    {
    id: 'Mony',
    name: 'Деньги',
    description: 'Универсальная валюта в этих краях. Принимается почти везде.',
    type: ItemType.RESOURCE, // или можно завести отдельный тип, например: ItemType.CURRENCY
    rarity: ItemRarity.COMMON,
    weight: 0, // или 0.001 если хочешь "реализм"
    value: 1,
    stackable: true,
    maxStack: 9999,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: false, // сами по себе не продаются
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Простой, но устойчивый эквивалент ценности в разрушенном мире.',
    createdAt: new Date(),
  },
  {
    id: 'metal-parts',
    name: 'Металлические детали',
    description: 'Различные металлические части и компоненты. Полезны для ремонта и создания.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 8,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Металлолом,',
    createdAt: new Date(),
  },

  {
    id: 'electronic_parts',
    name: 'Электронные компоненты',
    description: 'Микросхемы, провода и другие электронные детали.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.2,
    value: 15,
    stackable: true,
    maxStack: 50,
    effects: [],
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Нужен научный подход',
    createdAt: new Date(),
  },

  {
    id: 'cloth',
    name: 'Ткань',
    description: 'Куски ткани различного качества. Используется для пошива и ремонта.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 2,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Надеюсь это не был чей то носовой плоток',
    createdAt: new Date(),
  },

  {
    id: 'fusion_core',
    name: 'Ядерный блок',
    description: 'Портативный ядерный реактор. Источник энергии для силовой брони.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.RARE,
    weight: 3.0,
    value: 500,
    stackable: true,
    maxStack: 10,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чудо довоенной технологии. Содержит энергию небольшой звезды.',
    createdAt: new Date(),
  },

  {
    id: 'optical_elements',
    name: 'Оптические элементы',
    description: 'Линзы, призмы и другие компоненты для оптики.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.3,
    value: 25,
    stackable: true,
    maxStack: 30,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Хрупкие, но ценные элементы для точных приборов.',
    createdAt: new Date(),
  },

  {
    id: 'leather',
    name: 'Кожа',
    description: 'Обработанная кожа, используемая для создания брони и экипировки.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.4,
    value: 6,
    stackable: true,
    maxStack: 50,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Прочный и гибкий материал для выживания.',
    createdAt: new Date(),
  },

  {
    id: 'cork',
    name: 'Пробка',
    description: 'Лёгкий материал, часто используется в изоляции или уплотнении.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.05,
    value: 4,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Редкость среди остатков цивилизации.',
    createdAt: new Date(),
  },

  {
    id: 'fasteners',
    name: 'Метизы',
    description: 'Винты, гайки, болты и другие крепёжные элементы.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 3,
    stackable: true,
    maxStack: 200,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Незаменимы при сборке и ремонте.',
    createdAt: new Date(),
  },

  {
    id: 'springs',
    name: 'Пружины',
    description: 'Пружины разных размеров. Используются в механизмах и оружии.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.15,
    value: 5,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Маленькие, но важные детали любой механики.',
    createdAt: new Date(),
  },

  {
    id: 'glue',
    name: 'Клей',
    description: 'Сильный клей для склеивания различных материалов.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.RARE,
    weight: 0.2,
    value: 25,
    stackable: true,
    maxStack: 30,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Надёжен даже в самых суровых условиях.',
    createdAt: new Date(),
  }
  ,

  {
    id: 'chrome',
    name: 'Хром',
    description: 'Металл, используемый для покрытия и защиты.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.RARE,
    weight: 0.2,
    value: 30,
    stackable: true,
    maxStack: 30,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Самый качественный хром содранный с молдингов Хайвейминга',
    createdAt: new Date(),
  }
];


// Объединенный экспорт всех предметов
export const ALL_ITEMS: InventoryItem[] = [
  ...CONSUMABLES,
  ...TOOLS,
  ...RESOURCES
];
