import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { WorldsModule } from './worlds/worlds.module'
import { WorldEntity } from './entities/world.entity'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT) || 5432,
      username: process.env.DATABASE_USERNAME || 'nuclearstory',
      password: process.env.DATABASE_PASSWORD || 'password',
      database: process.env.DATABASE_NAME || 'saves_db',
      entities: [WorldEntity],
      synchronize: process.env.NODE_ENV === 'development', // Только для разработки!
      logging: process.env.NODE_ENV === 'development',
    }),
    WorldsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
