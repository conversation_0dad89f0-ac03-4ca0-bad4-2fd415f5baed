import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Получить информацию о сервисе' })
  @ApiResponse({ status: 200, description: 'Информация о сервисе' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: 'Проверка здоровья сервиса' })
  @ApiResponse({ status: 200, description: 'Сервис работает нормально' })
  getHealth() {
    return {
      status: 'ok',
      service: 'world-generator-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
