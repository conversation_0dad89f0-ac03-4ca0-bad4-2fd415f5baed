.settingsPanel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 0.5rem;
}

.setting {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.settingRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settingLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settingLabel svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #9ca3af;
}

.labelText {
  color: white;
  font-weight: 500;
}

.labelDescription {
  font-size: 0.875rem;
  color: #9ca3af;
}

.volumeContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.volumeValue {
  color: #d1d5db;
}

.sliderContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sliderContainer svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #9ca3af;
}

.slider {
  flex: 1;
  height: 0.5rem;
  background-color: #374151;
  border-radius: 0.5rem;
  appearance: none;
  cursor: pointer;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.toggleOff {
  background-color: #4b5563;
}

.toggleOn {
  background-color: #16a34a;
}

.toggleThumb {
  display: inline-block;
  height: 1rem;
  width: 1rem;
  transform: translateX(0.25rem);
  border-radius: 50%;
  background-color: white;
  transition: transform 0.3s ease;
}

.toggleThumbOn {
  transform: translateX(1.5rem);
}

.difficultyGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.difficultyButton {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.difficultyButtonInactive {
  background-color: #374151;
  color: #d1d5db;
}

.difficultyButtonInactive:hover {
  background-color: #4b5563;
}

.difficultyButtonActive {
  background-color: #3b82f6;
  color: white;
}

.difficultyDescription {
  font-size: 0.875rem;
  color: #9ca3af;
}

.resetSection {
  padding-top: 1.5rem;
  border-top: 1px solid #4b5563;
}

.resetButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background-color: #374151;
  color: white;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.resetButton:hover {
  background-color: #4b5563;
}

.resetButton svg {
  width: 1rem;
  height: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .settingRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .volumeContainer {
    width: 100%;
  }
  
  .sliderContainer {
    width: 100%;
  }
  
  .difficultyGrid {
    grid-template-columns: 1fr;
  }
}
