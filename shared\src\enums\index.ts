export enum UserRole {
  PLAYER = 'player',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

export enum GameStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  GAME_OVER = 'game_over'
}

export enum QuestStatus {
  AVAILABLE = 'available',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  LOCKED = 'locked'
}

export enum QuestType {
  MAIN = 'main',
  SIDE = 'side',
  RANDOM = 'random',
  SURVIVAL = 'survival'
}

export enum EventType {
  STORY = 'story',
  COMBAT = 'combat',
  CHOICE = 'choice',
  DISCOVERY = 'discovery',
  SURVIVAL = 'survival',
  RANDOM = 'random'
}

export enum ItemType {
  CONSUMABLE = 'consumable',
  WEAPON = 'weapon',
  ARMOR = 'armor',
  TOOL = 'tool',
  RESOURCE = 'resource',
  QUEST_ITEM = 'quest_item'
}

export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export enum LocationType {
  BUNKER = 'bunker',
  SHELTER = 'shelter',
  GARAGE = 'garage',
  WASTELAND = 'wasteland',
  RUINS = 'ruins',
  SETTLEMENT = 'settlement',
  FACTORY = 'factory',
  HOSPITAL = 'hospital',
  SCHOOL = 'school',
  MILITARY_BASE = 'military_base',
  VAULT = 'vault',
  CAVE = 'cave',
  BRIDGE = 'bridge',
  CHECKPOINT = 'checkpoint',
  SAFE = 'safe',
  DANGEROUS = 'dangerous',
  NEUTRAL = 'neutral',
  UNKNOWN = 'unknown'
}

export enum FactionAlignment {
  HOSTILE = 'hostile',
  NEUTRAL = 'neutral',
  FRIENDLY = 'friendly',
  ALLIED = 'allied'
}

export enum NPCType {
  TRADER = 'trader',
  RAIDER = 'raider',
  MERCENARY = 'mercenary',
  GUARD = 'guard',
  SCIENTIST = 'scientist',
  DOCTOR = 'doctor',
  ENGINEER = 'engineer',
  SCAVENGER = 'scavenger',
  MUTANT = 'mutant',
  ROBOT = 'robot',
  CIVILIAN = 'civilian',
  LEADER = 'leader'
}

export enum ContainerType {
  // Мебель
  NIGHTSTAND = 'nightstand',
  DRESSER = 'dresser',
  WARDROBE = 'wardrobe',
  DESK = 'desk',
  TABLE = 'table',
  SHELF = 'shelf',
  BOOKSHELF = 'bookshelf',
  CABINET = 'cabinet',

  // Хранилища
  CHEST = 'chest',
  TRUNK = 'trunk',
  LOCKER = 'locker',
  SAFE = 'safe',
  VAULT = 'vault',
  STRONGBOX = 'strongbox',

  // Ящики и коробки
  WOODEN_BOX = 'wooden_box',
  METAL_BOX = 'metal_box',
  PLASTIC_CRATE = 'plastic_crate',
  AMMO_BOX = 'ammo_box',
  SUPPLY_CRATE = 'supply_crate',
  TOOLBOX = 'toolbox',
  FIRST_AID_KIT = 'first_aid_kit',

  // Промышленные
  FILING_CABINET = 'filing_cabinet',
  STORAGE_UNIT = 'storage_unit',
  SHIPPING_CONTAINER = 'shipping_container',
  FUEL_TANK = 'fuel_tank',
  CHEMICAL_CONTAINER = 'chemical_container',

  // Бытовые
  REFRIGERATOR = 'refrigerator',
  FREEZER = 'freezer',
  OVEN = 'oven',
  MICROWAVE = 'microwave',
  WASHING_MACHINE = 'washing_machine',
  DISHWASHER = 'dishwasher',

  // Мусор и разное
  TRASH_CAN = 'trash_can',
  DUMPSTER = 'dumpster',
  BARREL = 'barrel',
  SUITCASE = 'suitcase',
  BACKPACK = 'backpack',
  PURSE = 'purse',

  // Специальные
  CORPSE = 'corpse',
  VEHICLE_TRUNK = 'vehicle_trunk',
  HIDDEN_STASH = 'hidden_stash',
  BURIED_CACHE = 'buried_cache'
}

export enum EffectType {
  HEALING = 'healing',
  POISON = 'poison',
  RADIATION = 'radiation',
  BUFF_STRENGTH = 'buff_strength',
  BUFF_PERCEPTION = 'buff_perception',
  BUFF_ENDURANCE = 'buff_endurance',
  BUFF_CHARISMA = 'buff_charisma',
  BUFF_INTELLIGENCE = 'buff_intelligence',
  BUFF_AGILITY = 'buff_agility',
  BUFF_LUCK = 'buff_luck',
  DEBUFF_STRENGTH = 'debuff_strength',
  DEBUFF_PERCEPTION = 'debuff_perception',
  DEBUFF_ENDURANCE = 'debuff_endurance',
  DEBUFF_CHARISMA = 'debuff_charisma',
  DEBUFF_INTELLIGENCE = 'debuff_intelligence',
  DEBUFF_AGILITY = 'debuff_agility',
  DEBUFF_LUCK = 'debuff_luck',
  STUN = 'stun',
  BLIND = 'blind',
  PARALYSIS = 'paralysis'
}

export enum BattlePhase {
  PREPARATION = 'preparation',
  PLAYER_TURN = 'player_turn',
  ENEMY_TURN = 'enemy_turn',
  RESULT = 'result',
  ENDED = 'ended'
}

export enum TerrainType {
  FOREST = 'forest',
  CITY = 'city',
  WASTELAND = 'wasteland',
  RUINS = 'ruins',
  DESERT = 'desert',
  SWAMP = 'swamp',
  MOUNTAIN = 'mountain',
  UNDERGROUND = 'underground',
  WATER = 'water',
  ROAD = 'road',
  RADIOACTIVE = 'radioactive'
}
