import { ItemType, ItemRarity } from '../enums';

export interface ArmorProtection {
  // Основная защита
  damageThreshold: number; // минимальный урон для пробития
  damageResistance: number; // процент поглощения урона (0-100)
  
  // Защита от типов урона
  resistances: {
    normal: number; // физический урон
    laser: number; // лазерное оружие
    plasma: number; // плазменное оружие
    explosive: number; // взрывчатка
    fire: number; // огонь
    electric: number; // электричество
    poison: number; // яды и токсины
    radiation: number; // радиация
    acid: number; // кислота
    cold: number; // холод
  };
  
  // Специальные защиты
  environmentalProtection: {
    radiation: number; // защита от радиации
    temperature: number; // защита от температур
    pressure: number; // защита от давления
    vacuum: boolean; // защита от вакуума
    underwater: boolean; // подводная защита
    chemical: number; // защита от химикатов
  };
}

export interface ArmorStats {
  protection: ArmorProtection;
  
  // Физические характеристики
  weight: number;
  encumbrance: number; // штраф к ловкости/скорости
  
  // Прочность
  durability: number;
  maxDurability: number;
  degradationRate: number;
  
  // Влияние на характеристики
  statModifiers: {
    strengthBonus?: number;
    perceptionPenalty?: number;
    enduranceBonus?: number;
    charismaModifier?: number;
    intelligencePenalty?: number;
    agilityPenalty?: number;
    luckModifier?: number;
  };
  
  // Влияние на навыки
  skillModifiers: {
    sneakPenalty?: number;
    lockpickPenalty?: number;
    repairBonus?: number;
    scienceBonus?: number;
    medicineBonus?: number;
    speechModifier?: number;
  };
  
  // Требования для ношения
  requirements: {
    strengthRequired: number;
    enduranceRequired: number;
    skillRequired?: Record<string, number>; // навык -> уровень
  };
  
  // Особые свойства
  specialProperties: {
    stealthDetection: boolean; // обнаруживает скрытых врагов
    nightVision: boolean; // ночное зрение
    thermalVision: boolean; // тепловизор
    breathingApparatus: boolean; // дыхательный аппарат
    communicationSystem: boolean; // система связи
    targetingSystem: boolean; // система наведения
    jumpJets: boolean; // реактивные двигатели
    magneticBoots: boolean; // магнитные ботинки
  };
}

export interface ArmorModification {
  id: string;
  name: string;
  description: string;
  slot: 'plating' | 'lining' | 'servos' | 'helmet_mod' | 'torso_mod' | 'limb_mod' | 'power_system';
  
  effects: {
    // Защита
    damageResistanceBonus?: number;
    damageThresholdBonus?: number;
    radiationResistanceBonus?: number;
    
    // Характеристики
    strengthBonus?: number;
    agilityBonus?: number;
    perceptionBonus?: number;
    
    // Вес и мобильность
    weightReduction?: number;
    encumbranceReduction?: number;
    
    // Прочность
    durabilityBonus?: number;
    
    // Особые эффекты
    addNightVision?: boolean;
    addThermalVision?: boolean;
    addStealthField?: boolean;
    addShielding?: boolean;
  };
  
  requirements?: {
    skill?: Record<string, number>;
    items?: string[]; // Component item IDs
    powerRequired?: number; // требуемая мощность
  };
  
  isInstalled: boolean;
  powerConsumption?: number; // потребление энергии
}

export interface Armor {
  id: string;
  name: string;
  description: string;
  type: ItemType; // должен быть ARMOR
  rarity: ItemRarity;

  // Basic properties
  weight: number;
  value: number;

  // Armor classification
  armorType: 'clothing' | 'light' | 'medium' | 'heavy' | 'power_armor' | 'environmental_suit';
  armorClass: 'jumpsuit' | 'leather' | 'metal' | 'combat' | 'riot' | 'military' | 'power' | 'hazmat' | 'space_suit';
  
  // Слоты экипировки
  equipmentSlots: ('head' | 'torso' | 'arms' | 'legs' | 'feet' | 'full_body')[];
  
  // Combat stats
  stats: ArmorStats;

  // Power system (для силовой брони)
  powerSystem?: {
    hasPowerSystem: boolean;
    maxPower: number;
    currentPower: number;
    powerConsumption: number; // в час
    powerSources: string[]; // типы источников питания
    batteryLife: number; // часов
  };
  
  // Modifications
  modificationSlots: string[]; // Available mod slots
  installedMods: ArmorModification[];
  maxMods: number;

  // Appearance and sound
  sprite?: string;
  icon?: string;
  soundEffects?: {
    equip?: string;
    unequip?: string;
    move?: string; // звук при движении
    damage?: string; // звук при получении урона
  };

  // Special abilities
  specialAbilities: string[]; // Ability IDs

  // Crafting and repair
  canBeCrafted: boolean;
  craftingRecipe?: {
    components: Record<string, number>; // Item ID -> quantity
    skill: Record<string, number>; // Skill -> level required
    tools?: string[]; // Tool item IDs
    workbench?: string; // требуемый верстак
  };
  canBeRepaired: boolean;
  repairCost?: Record<string, number>; // Item ID -> quantity
  repairSkill?: string; // навык для ремонта

  // Set bonuses (если часть комплекта)
  setInfo?: {
    setId: string;
    setName: string;
    setPieces: string[]; // ID других частей комплекта
    setBonuses: Array<{
      piecesRequired: number;
      bonus: {
        statBonus?: Record<string, number>;
        resistanceBonus?: Record<string, number>;
        specialAbility?: string;
      };
    }>;
  };

  // Environmental effects
  environmentalEffects: {
    // Влияние на окружающую среду
    heatSignature: number; // тепловая сигнатура
    noiseLevel: number; // уровень шума при движении
    visibility: number; // видимость (для маскировки)
    
    // Защита от окружающей среды
    weatherProtection: boolean;
    temperatureRange: {
      min: number; // минимальная температура
      max: number; // максимальная температура
    };
  };

  // Condition and maintenance
  condition: {
    currentCondition: number; // 0-100
    maintenanceRequired: boolean;
    lastMaintenanceAt?: Date;
    maintenanceInterval: number; // часов между обслуживанием
  };

  // Lore and flavor
  manufacturer?: string;
  model?: string;
  yearMade?: number;
  militaryDesignation?: string;
  loreText?: string;

  // Flags
  isUnique: boolean;
  isQuestItem: boolean;
  canBeSold: boolean;
  canBeDropped: boolean;
  canBeStolen: boolean;
  requiresTraining: boolean; // требует обучения для использования
  isRestricted: boolean; // ограниченный доступ

  // Timestamps
  createdAt: Date;
  lastUsedAt?: Date;
  lastRepairedAt?: Date;
  lastMaintenanceAt?: Date;
}
