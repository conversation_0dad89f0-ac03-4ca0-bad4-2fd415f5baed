import { Position } from '../models/Player'

export interface DoorLock {
  // Тип замка
  type: 'key' | 'keycard' | 'code' | 'biometric' | 'combination' | 'electronic' | 'magical'
  
  // Сложность взлома
  difficulty: number // 0-100
  
  // Требования для открытия
  requirements: {
    keyId?: string // ID ключа
    keycardLevel?: number // уровень доступа
    code?: string // цифровой код
    biometricId?: string // ID биометрических данных
    skillLevel?: number // уровень навыка взлома
    toolRequired?: string // ID инструмента
  }
  
  // Состояние замка
  isLocked: boolean
  isBroken: boolean
  isJammed: boolean
  
  // Попытки взлома
  failedAttempts: number
  maxAttempts: number
  lockoutTime?: number // минут блокировки после превышения попыток
  
  // Безопасность
  hasAlarm: boolean
  alarmTriggered: boolean
  isMonitored: boolean // наблюдается ли системой безопасности
}

export interface DoorMaterial {
  // Материал двери
  type: 'wood' | 'metal' | 'reinforced_metal' | 'glass' | 'plastic' | 'composite' | 'energy_barrier'
  
  // Прочность
  durability: number // текущая прочность
  maxDurability: number // максимальная прочность
  
  // Сопротивления
  resistances: {
    physical: number // сопротивление физическому урону
    fire: number // сопротивление огню
    acid: number // сопротивление кислоте
    explosion: number // сопротивление взрывам
    energy: number // сопротивление энергетическому оружию
  }
  
  // Свойства
  isTransparent: boolean // можно ли видеть сквозь дверь
  isSoundproof: boolean // звукоизоляция
  isFireproof: boolean // огнестойкость
  isRadiationShielded: boolean // защита от радиации
}

export interface DoorAnimation {
  // Тип анимации
  openType: 'swing' | 'slide' | 'fold' | 'roll' | 'iris' | 'phase' | 'teleport'
  
  // Направление открытия
  openDirection: 'inward' | 'outward' | 'left' | 'right' | 'up' | 'down'
  
  // Скорость анимации
  openSpeed: number // секунд для полного открытия
  closeSpeed: number // секунд для полного закрытия
  
  // Автоматическое закрытие
  autoClose: boolean
  autoCloseDelay: number // секунд до автозакрытия
  
  // Звуки
  openSound?: string
  closeSound?: string
  lockedSound?: string
  breakSound?: string
}

export interface Door {
  // Основная информация
  id: string
  name: string
  description: string
  
  // Позиция и ориентация
  position: Position
  rotation: number // градусы (0-360)
  
  // Размеры
  width: number
  height: number
  thickness: number
  
  // Состояние двери
  isOpen: boolean
  isDestroyed: boolean
  isBlocked: boolean // заблокирована объектом
  blockingObjectId?: string
  
  // Материал и прочность
  material: DoorMaterial
  
  // Замок
  lock?: DoorLock
  
  // Анимация и поведение
  animation: DoorAnimation
  
  // Связи и переходы
  connections: {
    // Переход между локациями
    leadsToLocation?: {
      locationId: string
      targetPosition: Position
      targetLevel?: number
    }
    
    // Переход между уровнями в одной локации
    leadsToLevel?: {
      targetLevel: number
      targetPosition: Position
    }
    
    // Переход в другую ячейку той же локации
    leadsToCell?: {
      targetPosition: Position
      sameLevel: boolean
    }
  }
  
  // Доступ и разрешения
  access: {
    // Кто может проходить
    allowedFactions: string[] // ID фракций
    allowedNPCs: string[] // ID конкретных NPC
    allowedPlayers: string[] // ID игроков
    
    // Ограничения
    requiresPermission: boolean
    permissionLevel: number // уровень доступа
    
    // Временные ограничения
    accessSchedule?: Array<{
      startHour: number
      endHour: number
      daysOfWeek: number[] // 0-6, воскресенье = 0
    }>
  }
  
  // Взаимодействие
  interaction: {
    // Можно ли взаимодействовать
    isInteractable: boolean
    
    // Расстояние взаимодействия
    interactionRange: number
    
    // Время взаимодействия
    interactionTime: number // секунд
    
    // Требования для взаимодействия
    requirements?: {
      strength?: number // минимальная сила
      skill?: string // требуемый навык
      tool?: string // требуемый инструмент
      energy?: number // требуемая энергия
    }
  }
  
  // Эффекты и последствия
  effects: {
    // При открытии
    onOpen?: {
      triggerAlarm?: boolean
      alertNPCs?: string[] // ID NPC для оповещения
      spawnEnemies?: string[] // ID врагов для спавна
      giveItems?: string[] // ID предметов для получения
      startQuest?: string // ID квеста для запуска
      changeReputation?: Record<string, number> // фракция -> изменение
    }
    
    // При закрытии
    onClose?: {
      lockAutomatically?: boolean
      sealPermanently?: boolean
      triggerTrap?: string // ID ловушки
    }
    
    // При разрушении
    onDestroy?: {
      createDebris?: boolean
      blockPassage?: boolean
      triggerExplosion?: boolean
      alertArea?: number // радиус оповещения
    }
  }
  
  // Визуальные эффекты
  visual: {
    sprite?: string
    model?: string
    texture?: string
    
    // Эффекты состояния
    glowWhenLocked?: boolean
    sparkWhenDamaged?: boolean
    smokeWhenDestroyed?: boolean
    
    // Индикаторы
    hasStatusLight?: boolean
    statusLightColor?: string // цвет индикатора состояния
  }
  
  // История и статистика
  history: {
    timesOpened: number
    timesLocked: number
    timesBroken: number
    lastOpenedAt?: Date
    lastLockedAt?: Date
    lastRepairedAt?: Date
    
    // Кто последний взаимодействовал
    lastInteractedBy?: {
      type: 'player' | 'npc'
      id: string
      action: 'open' | 'close' | 'lock' | 'unlock' | 'break' | 'repair'
    }
  }
  
  // Метаданные
  createdAt: Date
  lastUpdatedAt: Date
  version: string
}
