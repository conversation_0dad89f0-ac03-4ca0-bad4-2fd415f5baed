import React, { useState } from 'react'
import { WorldCreator } from './WorldCreator'
import { WorldsList } from './WorldsList'
import { useWorld, useUserWorldsStats } from '../hooks/useWorlds'

interface WorldManagerProps {
  userId: string
  onWorldLoaded?: (world: any) => void
}

export const WorldManager: React.FC<WorldManagerProps> = ({ userId, onWorldLoaded }) => {
  const [activeTab, setActiveTab] = useState<'list' | 'create' | 'stats'>('list')
  const [selectedWorldId, setSelectedWorldId] = useState<string | null>(null)
  
  const { world: selectedWorld, loading: worldLoading } = useWorld(selectedWorldId, userId)
  const { stats, loading: statsLoading } = useUserWorldsStats(userId)

  const handleWorldCreated = (worldId: string) => {
    setSelectedWorldId(worldId)
    setActiveTab('list')
  }

  const handleWorldLoad = async (worldId: string) => {
    setSelectedWorldId(worldId)
    
    // Загружаем полные данные мира
    if (selectedWorld && selectedWorldId === worldId) {
      onWorldLoaded?.(selectedWorld)
    }
  }

  const formatSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return mb < 1 ? `${Math.round(bytes / 1024)}KB` : `${mb.toFixed(1)}MB`
  }

  return (
    <div className="world-manager">
      <div className="world-manager-header">
        <h1>Управление мирами</h1>
        
        <div className="world-manager-tabs">
          <button
            className={`tab ${activeTab === 'list' ? 'active' : ''}`}
            onClick={() => setActiveTab('list')}
          >
            📋 Мои миры
          </button>
          
          <button
            className={`tab ${activeTab === 'create' ? 'active' : ''}`}
            onClick={() => setActiveTab('create')}
          >
            ➕ Создать мир
          </button>
          
          <button
            className={`tab ${activeTab === 'stats' ? 'active' : ''}`}
            onClick={() => setActiveTab('stats')}
          >
            📊 Статистика
          </button>
        </div>
      </div>

      <div className="world-manager-content">
        {activeTab === 'list' && (
          <WorldsList
            userId={userId}
            onWorldSelect={setSelectedWorldId}
            onWorldLoad={handleWorldLoad}
          />
        )}

        {activeTab === 'create' && (
          <WorldCreator
            userId={userId}
            onWorldCreated={handleWorldCreated}
          />
        )}

        {activeTab === 'stats' && (
          <div className="world-stats-tab">
            <h2>Статистика миров</h2>
            
            {statsLoading ? (
              <div className="stats-loading">Загрузка статистики...</div>
            ) : stats ? (
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">{stats.totalWorlds}</div>
                  <div className="stat-label">Всего миров</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{stats.activeWorlds}</div>
                  <div className="stat-label">Активных миров</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{stats.archivedWorlds}</div>
                  <div className="stat-label">Архивированных</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{stats.totalLoadCount}</div>
                  <div className="stat-label">Всего загрузок</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{formatSize(stats.averageWorldSize)}</div>
                  <div className="stat-label">Средний размер мира</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">
                    {stats.totalWorlds > 0 ? Math.round(stats.totalLoadCount / stats.totalWorlds) : 0}
                  </div>
                  <div className="stat-label">Загрузок на мир</div>
                </div>
              </div>
            ) : (
              <div className="stats-error">Не удалось загрузить статистику</div>
            )}
          </div>
        )}
      </div>

      {/* Боковая панель с информацией о выбранном мире */}
      {selectedWorldId && (
        <div className="world-details-sidebar">
          <h3>Детали мира</h3>
          
          {worldLoading ? (
            <div className="world-details-loading">Загрузка...</div>
          ) : selectedWorld ? (
            <div className="world-details">
              <div className="world-detail-item">
                <span className="label">Название:</span>
                <span>{selectedWorld.metadata.name}</span>
              </div>
              
              <div className="world-detail-item">
                <span className="label">ID:</span>
                <code>{selectedWorld.id}</code>
              </div>
              
              <div className="world-detail-item">
                <span className="label">Seed:</span>
                <code>{selectedWorld.settings.seed}</code>
              </div>
              
              <div className="world-detail-item">
                <span className="label">Размер:</span>
                <span>
                  {selectedWorld.settings.worldSize.width}×{selectedWorld.settings.worldSize.height}
                </span>
              </div>
              
              <div className="world-detail-item">
                <span className="label">Сложность:</span>
                <span>{selectedWorld.settings.difficulty}</span>
              </div>
              
              <div className="world-detail-item">
                <span className="label">Локаций:</span>
                <span>{Object.keys(selectedWorld.locations || {}).length}</span>
              </div>
              
              <div className="world-detail-item">
                <span className="label">NPC:</span>
                <span>{Object.keys(selectedWorld.npcs || {}).length}</span>
              </div>
              
              <div className="world-detail-item">
                <span className="label">Создан:</span>
                <span>
                  {new Date(selectedWorld.metadata.createdAt).toLocaleDateString('ru-RU')}
                </span>
              </div>
              
              <div className="world-actions">
                <button
                  onClick={() => handleWorldLoad(selectedWorldId)}
                  className="btn btn-primary"
                >
                  🎮 Загрузить в игру
                </button>
                
                <button
                  onClick={() => setSelectedWorldId(null)}
                  className="btn btn-secondary"
                >
                  ✕ Закрыть
                </button>
              </div>
            </div>
          ) : (
            <div className="world-details-error">
              Не удалось загрузить детали мира
            </div>
          )}
        </div>
      )}
    </div>
  )
}
