version: '3.8'

services:
  world-generator-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: world-generator-service-dev
    ports:
      - "3003:3003"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3003
    restart: unless-stopped
    networks:
      - nuclear-story-network

networks:
  nuclear-story-network:
    external: true
