import React from 'react'
import { Volume2, VolumeX, Music, Speaker, Save, RotateCcw } from 'lucide-react'
import { useGameStore } from '../store/gameStore'
import styles from './SettingsPanel.module.css'

const SettingsPanel: React.FC = () => {
  const { settings, updateSettings } = useGameStore()

  const handleVolumeChange = (value: number) => {
    updateSettings({ volume: value / 100 })
  }

  const handleSoundToggle = () => {
    updateSettings({ soundEnabled: !settings.soundEnabled })
  }

  const handleMusicToggle = () => {
    updateSettings({ musicEnabled: !settings.musicEnabled })
  }

  const handleDifficultyChange = (difficulty: 'easy' | 'normal' | 'hard') => {
    updateSettings({ difficulty })
  }

  const handleAutoSaveToggle = () => {
    updateSettings({ autoSave: !settings.autoSave })
  }

  const resetToDefaults = () => {
    if (confirm('Сбросить все настройки к значениям по умолчанию?')) {
      updateSettings({
        volume: 0.7,
        soundEnabled: true,
        musicEnabled: true,
        difficulty: 'normal',
        autoSave: true
      })
    }
  }

  return (
    <div className={styles.settingsPanel}>
      {/* Audio Settings */}
      <div className={styles.section}>
        <h3 className={styles.sectionTitle}>
          🔊 Аудио настройки
        </h3>

        {/* Master Volume */}
        <div className={styles.setting}>
          <div className={styles.volumeContainer}>
            <label className={styles.labelText}>Общая громкость</label>
            <span className={styles.volumeValue}>{Math.round(settings.volume * 100)}%</span>
          </div>
          <div className={styles.sliderContainer}>
            <VolumeX />
            <input
              type="range"
              min="0"
              max="100"
              value={Math.round(settings.volume * 100)}
              onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
              className={styles.slider}
            />
            <Volume2 />
          </div>
        </div>

        {/* Sound Effects Toggle */}
        <div className={styles.settingRow}>
          <div className={styles.settingLabel}>
            <Speaker />
            <label className={styles.labelText}>Звуковые эффекты</label>
          </div>
          <button
            onClick={handleSoundToggle}
            className={`${styles.toggle} ${settings.soundEnabled ? styles.toggleOn : styles.toggleOff}`}
          >
            <span
              className={`${styles.toggleThumb} ${settings.soundEnabled ? styles.toggleThumbOn : ''}`}
            />
          </button>
        </div>

        {/* Music Toggle */}
        <div className={styles.settingRow}>
          <div className={styles.settingLabel}>
            <Music />
            <label className={styles.labelText}>Фоновая музыка</label>
          </div>
          <button
            onClick={handleMusicToggle}
            className={`${styles.toggle} ${settings.musicEnabled ? styles.toggleOn : styles.toggleOff}`}
          >
            <span
              className={`${styles.toggleThumb} ${settings.musicEnabled ? styles.toggleThumbOn : ''}`}
            />
          </button>
        </div>
      </div>

      {/* Game Settings */}
      <div className={styles.section}>
        <h3 className={styles.sectionTitle}>
          🎮 Игровые настройки
        </h3>

        {/* Difficulty */}
        <div className={styles.setting}>
          <label className={styles.labelText}>Сложность</label>
          <div className={styles.difficultyGrid}>
            {(['easy', 'normal', 'hard'] as const).map((diff) => (
              <button
                key={diff}
                onClick={() => handleDifficultyChange(diff)}
                className={`${styles.difficultyButton} ${
                  settings.difficulty === diff
                    ? styles.difficultyButtonActive
                    : styles.difficultyButtonInactive
                }`}
              >
                {diff === 'easy' && 'Легко'}
                {diff === 'normal' && 'Нормально'}
                {diff === 'hard' && 'Сложно'}
              </button>
            ))}
          </div>
          <p className={styles.difficultyDescription}>
            {settings.difficulty === 'easy' && 'Больше ресурсов, меньше опасностей'}
            {settings.difficulty === 'normal' && 'Сбалансированный игровой процесс'}
            {settings.difficulty === 'hard' && 'Ограниченные ресурсы, высокие риски'}
          </p>
        </div>

        {/* Auto Save */}
        <div className={styles.settingRow}>
          <div className={styles.settingLabel}>
            <Save />
            <div>
              <label className={styles.labelText}>Автосохранение</label>
              <p className={styles.labelDescription}>Автоматически сохранять прогресс</p>
            </div>
          </div>
          <button
            onClick={handleAutoSaveToggle}
            className={`${styles.toggle} ${settings.autoSave ? styles.toggleOn : styles.toggleOff}`}
          >
            <span
              className={`${styles.toggleThumb} ${settings.autoSave ? styles.toggleThumbOn : ''}`}
            />
          </button>
        </div>
      </div>

      {/* Reset Button */}
      <div className={styles.resetSection}>
        <button
          onClick={resetToDefaults}
          className={styles.resetButton}
        >
          <RotateCcw />
          Сбросить к настройкам по умолчанию
        </button>
      </div>


    </div>
  )
}

export default SettingsPanel
