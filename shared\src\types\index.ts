import { User<PERSON><PERSON>, GameStatus, QuestStatus, QuestType, EventType, ItemType, ItemRarity, LocationType } from '../enums';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface GameStats {
  health: number;
  hunger: number;
  thirst: number;
  radiation: number;
  energy: number;
  sanity?: number;
}


export interface Coordinates {
  x: number;
  y: number;
  z?: number;
}

export interface Choice {
  id: string;
  text: string;
  consequences?: {
    stats?: Partial<GameStats>;
    items?: string[];
    unlockQuests?: string[];
    unlockLocations?: string[];
  };
}

export interface StoryEvent {
  id: string;
  type: EventType;
  title: string;
  description: string;
  choices?: Choice[];
  requirements?: {
    level?: number;
    items?: string[];
    completedQuests?: string[];
    stats?: Partial<GameStats>;
  };
}

export interface GameSession {
  id: string;
  userId: string;
  status: GameStatus;
  currentLocation: string;
  gameStats: GameStats;
  startedAt: Date;
  lastPlayedAt: Date;
  playtimeMinutes: number;
}

// Export new type modules
export * from './NPC'
export * from './Location'
export * from './LocationGrid'
export * from './MapCell'
export * from './Unit'
export * from './Weapon'
export * from './Armor'
export * from './Faction'
export * from './Event'
export * from './World'
export * from './Door'
export * from './Container'
