# NuclearStory - Development Logs Script (PowerShell)
param(
    [string]$Service = "all"
)

Write-Host "📊 Viewing logs for: $Service" -ForegroundColor Cyan

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running." -ForegroundColor Red
    exit 1
}

if ($Service -eq "all") {
    Write-Host "📋 Showing logs for all services..." -ForegroundColor Green
    docker compose -f docker-compose.dev.yml logs -f
} else {
    Write-Host "📋 Showing logs for service: $Service" -ForegroundColor Green
    docker compose -f docker-compose.dev.yml logs -f $Service
}

Write-Host ""
Write-Host "Available services:" -ForegroundColor Yellow
Write-Host "  - auth-service" -ForegroundColor White
Write-Host "  - game-engine-service" -ForegroundColor White
Write-Host "  - story-service" -ForegroundColor White
Write-Host "  - save-service" -ForegroundColor White
Write-Host "  - ai-service" -ForegroundColor White
Write-Host "  - frontend" -ForegroundColor White
Write-Host "  - nginx" -ForegroundColor White
Write-Host "  - postgres-auth" -ForegroundColor White
Write-Host "  - postgres-saves" -ForegroundColor White
Write-Host "  - redis" -ForegroundColor White
Write-Host ""
Write-Host "Usage: .\scripts\dev-logs.ps1 [service-name]" -ForegroundColor Cyan
