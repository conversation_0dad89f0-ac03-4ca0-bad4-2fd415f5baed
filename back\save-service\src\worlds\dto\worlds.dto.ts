import { IsString, IsObject, IsNumber, IsBoolean, IsOptional, IsEnum, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
// Временный тип World
interface World {
  id: string
  name: string
  seed: string
  userId: string
  worldData: any
  createdAt: string
  updatedAt: string
}

/**
 * DTO для сохранения мира
 */
export class SaveWorldDto {
  @ApiProperty({
    description: 'User ID who created the world',
    example: 'user_123'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: 'Complete world data object',
    type: 'object'
  })
  @IsObject()
  worldData: World

  @ApiProperty({
    description: 'Time taken to generate the world in milliseconds',
    example: 5000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  generationTime?: number

  @ApiProperty({
    description: 'Whether AI was used in generation',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  generatedWithAI?: boolean
}

/**
 * DTO для обновления мира
 */
export class UpdateWorldDto {
  @ApiProperty({
    description: 'User ID who owns the world',
    example: 'user_123'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: 'New world name',
    example: 'My Awesome World',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string

  @ApiProperty({
    description: 'New world description',
    example: 'A challenging post-apocalyptic world',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({
    description: 'Whether the world should be public',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean

  @ApiProperty({
    description: 'World status',
    enum: ['active', 'archived'],
    required: false
  })
  @IsOptional()
  @IsEnum(['active', 'archived'])
  status?: 'active' | 'archived'
}

/**
 * DTO для краткой информации о мире
 */
export class WorldSummaryDto {
  @ApiProperty({
    description: 'World database ID',
    example: 'uuid-here'
  })
  id: string

  @ApiProperty({
    description: 'User ID who created the world',
    example: 'user_123'
  })
  userId: string

  @ApiProperty({
    description: 'World generation seed',
    example: 'nuclear_wasteland_2077'
  })
  seed: string

  @ApiProperty({
    description: 'World name',
    example: 'My Awesome World'
  })
  name: string

  @ApiProperty({
    description: 'World description',
    example: 'A challenging post-apocalyptic world'
  })
  description: string

  @ApiProperty({
    description: 'World metadata',
    type: 'object'
  })
  metadata: {
    worldSize: {
      width: number
      height: number
    }
    difficulty: string
    algorithm: string
    stats: {
      locationsCount: number
      npcsCount: number
      factionsCount: number
      cellsCount: number
    }
    tags: string[]
    generatorVersion: string
    generationTime: number
    generatedWithAI: boolean
  }

  @ApiProperty({
    description: 'World status',
    enum: ['active', 'archived', 'deleted']
  })
  status: 'active' | 'archived' | 'deleted'

  @ApiProperty({
    description: 'Whether the world is public',
    example: false
  })
  isPublic: boolean

  @ApiProperty({
    description: 'Number of times the world was loaded',
    example: 5
  })
  loadCount: number

  @ApiProperty({
    description: 'Last time the world was loaded',
    example: '2023-12-31T23:59:59.000Z'
  })
  lastLoadedAt: Date

  @ApiProperty({
    description: 'World size in bytes',
    example: 1048576
  })
  sizeBytes: number

  @ApiProperty({
    description: 'World version',
    example: '1.0.0'
  })
  version: string

  @ApiProperty({
    description: 'Creation date',
    example: '2023-12-31T23:59:59.000Z'
  })
  createdAt: Date

  @ApiProperty({
    description: 'Last update date',
    example: '2023-12-31T23:59:59.000Z'
  })
  updatedAt: Date
}

/**
 * DTO для ответа с мирами и пагинацией
 */
export class WorldsListResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true
  })
  success: boolean

  @ApiProperty({
    description: 'Array of world summaries',
    type: [WorldSummaryDto]
  })
  worlds: WorldSummaryDto[]

  @ApiProperty({
    description: 'Pagination information',
    type: 'object'
  })
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

/**
 * DTO для статистики миров пользователя
 */
export class UserWorldsStatsDto {
  @ApiProperty({
    description: 'Total number of worlds',
    example: 10
  })
  totalWorlds: number

  @ApiProperty({
    description: 'Number of active worlds',
    example: 8
  })
  activeWorlds: number

  @ApiProperty({
    description: 'Number of archived worlds',
    example: 2
  })
  archivedWorlds: number

  @ApiProperty({
    description: 'Total load count across all worlds',
    example: 150
  })
  totalLoadCount: number

  @ApiProperty({
    description: 'Average world size in bytes',
    example: 1048576
  })
  averageWorldSize: number
}

/**
 * DTO для статистики хранилища
 */
export class StorageStatsDto {
  @ApiProperty({
    description: 'Total number of worlds in storage',
    example: 1000
  })
  totalWorlds: number

  @ApiProperty({
    description: 'Total storage size in bytes',
    example: 1073741824
  })
  totalSizeBytes: number

  @ApiProperty({
    description: 'Total storage size in MB',
    example: 1024
  })
  totalSizeMB: number

  @ApiProperty({
    description: 'Average world size in bytes',
    example: 1048576
  })
  averageSizeBytes: number

  @ApiProperty({
    description: 'Average world size in MB',
    example: 1
  })
  averageSizeMB: number

  @ApiProperty({
    description: 'Largest world size in bytes',
    example: 5242880
  })
  largestWorldSize: number

  @ApiProperty({
    description: 'Largest world size in MB',
    example: 5
  })
  largestWorldSizeMB: number
}
